﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\src\format.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\src\os.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\include\fmt\args.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\include\fmt\chrono.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\include\fmt\color.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\include\fmt\compile.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\include\fmt\core.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\include\fmt\format.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\include\fmt\format-inl.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\include\fmt\os.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\include\fmt\ostream.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\include\fmt\printf.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\include\fmt\ranges.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\include\fmt\std.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\include\fmt\xchar.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\README.rst" />
    <None Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\ChangeLog.rst" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{B51AFE03-D7FA-3F7D-BDDF-881A2EF2E39B}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{ED3CB8E5-FD7A-3664-A7D3-A35456B4DB16}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
