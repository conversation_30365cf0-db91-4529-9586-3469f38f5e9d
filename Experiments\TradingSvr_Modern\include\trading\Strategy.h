/**
 * @file Strategy.h
 * @brief Modern strategy interface and base implementation
 * <AUTHOR> Team
 * @date 2024
 */

#pragma once

#include "Types.h"
#include "Events.h"
#include <functional>
#include <future>
#include <atomic>
#include <mutex>

namespace RoboQuant::Trading {

// Forward declarations
class Portfolio;
class OrderManager;
class DataProvider;

/**
 * @brief Abstract base class for all trading strategies
 */
class Strategy {
public:
    explicit Strategy(StrategyConfig config);
    virtual ~Strategy() = default;

    // Non-copyable, movable
    Strategy(const Strategy&) = delete;
    Strategy& operator=(const Strategy&) = delete;
    Strategy(Strategy&&) = default;
    Strategy& operator=(Strategy&&) = default;

    // Core lifecycle methods
    virtual std::future<bool> initialize() = 0;
    virtual std::future<void> start() = 0;
    virtual std::future<void> stop() = 0;
    virtual void shutdown() = 0;

    // Event handlers
    virtual void on_market_data(const QuoteData& quote) {}
    virtual void on_bar_data(const BarData& bar) {}
    virtual void on_order_fill(const OrderFill& fill) {}
    virtual void on_order_update(const Order& order) {}
    virtual void on_timer(const std::string& timer_id) {}

    // Strategy control
    [[nodiscard]] StrategyState get_state() const noexcept;
    [[nodiscard]] const StrategyConfig& get_config() const noexcept;
    [[nodiscard]] bool is_enabled() const noexcept;
    
    void enable() noexcept;
    void disable() noexcept;
    
    // Performance and risk
    [[nodiscard]] PerformanceMetrics get_performance() const;
    [[nodiscard]] RiskLimits get_risk_limits() const noexcept;
    void set_risk_limits(const RiskLimits& limits);

    // Portfolio access
    void set_portfolio(SharedPtr<Portfolio> portfolio);
    [[nodiscard]] SharedPtr<Portfolio> get_portfolio() const;

    // Order management
    void set_order_manager(SharedPtr<OrderManager> order_manager);
    [[nodiscard]] SharedPtr<OrderManager> get_order_manager() const;

    // Data access
    void set_data_provider(SharedPtr<DataProvider> data_provider);
    [[nodiscard]] SharedPtr<DataProvider> get_data_provider() const;

protected:
    // Helper methods for derived classes
    [[nodiscard]] bool can_trade() const noexcept;
    [[nodiscard]] bool check_risk_limits(const OrderRequest& request) const;
    
    void log_info(const std::string& message) const;
    void log_warning(const std::string& message) const;
    void log_error(const std::string& message) const;

    // State management
    void set_state(StrategyState state) noexcept;
    
    // Configuration access
    template<typename T>
    [[nodiscard]] std::optional<T> get_parameter(const std::string& key) const {
        return config_.get_parameter<T>(key);
    }

private:
    StrategyConfig config_;
    std::atomic<StrategyState> state_{StrategyState::Stopped};
    std::atomic<bool> enabled_{true};
    
    RiskLimits risk_limits_;
    mutable std::shared_mutex risk_limits_mutex_;
    
    WeakPtr<Portfolio> portfolio_;
    WeakPtr<OrderManager> order_manager_;
    WeakPtr<DataProvider> data_provider_;
    
    mutable std::shared_mutex components_mutex_;
    
    // Performance tracking
    mutable PerformanceMetrics performance_;
    mutable std::mutex performance_mutex_;
};

/**
 * @brief Strategy factory interface
 */
class StrategyFactory {
public:
    virtual ~StrategyFactory() = default;
    
    virtual UniquePtr<Strategy> create_strategy(const StrategyConfig& config) = 0;
    virtual std::vector<std::string> get_supported_strategies() const = 0;
};

/**
 * @brief Template-based strategy factory
 */
template<typename StrategyType>
class ConcreteStrategyFactory : public StrategyFactory {
public:
    UniquePtr<Strategy> create_strategy(const StrategyConfig& config) override {
        return std::make_unique<StrategyType>(config);
    }
    
    std::vector<std::string> get_supported_strategies() const override {
        return {StrategyType::strategy_name()};
    }
};

/**
 * @brief Strategy registry for managing strategy factories
 */
class StrategyRegistry {
public:
    static StrategyRegistry& instance();
    
    template<typename StrategyType>
    void register_strategy() {
        auto factory = std::make_unique<ConcreteStrategyFactory<StrategyType>>();
        register_factory(StrategyType::strategy_name(), std::move(factory));
    }
    
    void register_factory(const std::string& name, UniquePtr<StrategyFactory> factory);
    
    [[nodiscard]] UniquePtr<Strategy> create_strategy(
        const std::string& strategy_type, 
        const StrategyConfig& config
    ) const;
    
    [[nodiscard]] std::vector<std::string> get_available_strategies() const;

private:
    StrategyRegistry() = default;
    
    mutable std::shared_mutex factories_mutex_;
    std::unordered_map<std::string, UniquePtr<StrategyFactory>> factories_;
};

/**
 * @brief RAII strategy manager
 */
class StrategyManager {
public:
    StrategyManager() = default;
    ~StrategyManager();

    // Non-copyable, movable
    StrategyManager(const StrategyManager&) = delete;
    StrategyManager& operator=(const StrategyManager&) = delete;
    StrategyManager(StrategyManager&&) = default;
    StrategyManager& operator=(StrategyManager&&) = default;

    // Strategy management
    std::future<bool> add_strategy(UniquePtr<Strategy> strategy);
    std::future<bool> remove_strategy(const StrategyId& id);
    
    [[nodiscard]] Strategy* get_strategy(const StrategyId& id) const;
    [[nodiscard]] std::vector<Strategy*> get_all_strategies() const;
    
    // Lifecycle management
    std::future<void> start_all();
    std::future<void> stop_all();
    void shutdown_all();
    
    // Event distribution
    void distribute_market_data(const QuoteData& quote);
    void distribute_bar_data(const BarData& bar);
    void distribute_order_fill(const OrderFill& fill);
    void distribute_order_update(const Order& order);

private:
    mutable std::shared_mutex strategies_mutex_;
    std::unordered_map<StrategyId, UniquePtr<Strategy>> strategies_;
};

} // namespace RoboQuant::Trading
