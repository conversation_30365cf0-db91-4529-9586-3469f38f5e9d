/**
 * @file main.cpp
 * @brief Example main program for modern trading server
 * <AUTHOR> Team
 * @date 2024
 */

#include "trading/Trading.h"
#include <iostream>
#include <signal.h>
#include <thread>
#include <chrono>

using namespace RoboQuant::Trading;

// Global server instance for signal handling
std::unique_ptr<TradingServer> g_server;
std::atomic<bool> g_shutdown_requested{false};

// Signal handler for graceful shutdown
void signal_handler(int signal) {
    std::cout << "\nReceived signal " << signal << ", initiating graceful shutdown..." << std::endl;
    g_shutdown_requested = true;
    
    if (g_server) {
        auto stop_future = g_server->stop();
        // Wait for shutdown with timeout
        if (stop_future.wait_for(std::chrono::seconds(30)) == std::future_status::timeout) {
            std::cerr << "Warning: Server shutdown timed out, forcing exit" << std::endl;
        }
    }
}

// Setup signal handlers
void setup_signal_handlers() {
    signal(SIGINT, signal_handler);   // Ctrl+C
    signal(SIGTERM, signal_handler);  // Termination request
#ifdef _WIN32
    signal(SIGBREAK, signal_handler); // Ctrl+Break on Windows
#else
    signal(SIGHUP, signal_handler);   // Hangup signal on Unix
#endif
}

// Print server status
void print_status(const TradingServer& server) {
    std::cout << "\n=== Trading Server Status ===" << std::endl;
    std::cout << "Status: " << (server.is_running() ? "Running" : "Stopped") << std::endl;
    std::cout << "Version: " << get_version() << std::endl;
    
    auto health = server.get_health_status();
    std::cout << "Health Status:" << std::endl;
    for (const auto& [component, status] : health) {
        std::cout << "  " << component << ": " << status << std::endl;
    }
    
    auto portfolio_ids = server.get_portfolio_ids();
    std::cout << "Portfolios (" << portfolio_ids.size() << "):" << std::endl;
    for (const auto& id : portfolio_ids) {
        std::cout << "  " << id << std::endl;
    }
    
    auto models = server.get_loaded_models();
    std::cout << "Models (" << models.size() << "):" << std::endl;
    for (const auto& model : models) {
        std::cout << "  " << model << std::endl;
    }
    
    std::cout << "============================\n" << std::endl;
}

// Monitor server performance
void monitor_performance(const TradingServer& server) {
    while (!g_shutdown_requested) {
        std::this_thread::sleep_for(std::chrono::minutes(1));
        
        if (!server.is_running()) {
            break;
        }
        
        auto overall_perf = server.get_overall_performance();
        auto strategy_perf = server.get_strategy_performance();
        auto portfolio_perf = server.get_portfolio_performance();
        
        std::cout << "\n=== Performance Report ===" << std::endl;
        std::cout << "Overall P&L: " << Utils::format_currency(overall_perf.total_return) << std::endl;
        std::cout << "Unrealized P&L: " << Utils::format_currency(overall_perf.unrealized_pnl) << std::endl;
        std::cout << "Realized P&L: " << Utils::format_currency(overall_perf.realized_pnl) << std::endl;
        std::cout << "Total Trades: " << overall_perf.total_trades << std::endl;
        
        if (!strategy_perf.empty()) {
            std::cout << "\nStrategy Performance:" << std::endl;
            for (const auto& [strategy_id, perf] : strategy_perf) {
                std::cout << "  " << strategy_id << ": " 
                         << Utils::format_currency(perf.total_return) 
                         << " (" << perf.total_trades << " trades)" << std::endl;
            }
        }
        
        if (!portfolio_perf.empty()) {
            std::cout << "\nPortfolio Performance:" << std::endl;
            for (const auto& [portfolio_id, perf] : portfolio_perf) {
                std::cout << "  " << portfolio_id << ": " 
                         << Utils::format_currency(perf.total_return) << std::endl;
            }
        }
        std::cout << "=========================\n" << std::endl;
    }
}

int main(int argc, char* argv[]) {
    try {
        // Initialize the trading system
        std::cout << "Initializing RoboQuant Trading System v" << get_version() << std::endl;
        initialize();
        
        // Setup signal handlers for graceful shutdown
        setup_signal_handlers();
        
        // Load configuration
        std::string config_file = "config/server_config.json";
        if (argc > 1) {
            config_file = argv[1];
        }
        
        auto server_config = Config::load_server_config(config_file);
        if (!server_config) {
            std::cerr << "Failed to load server configuration from: " << config_file << std::endl;
            std::cerr << "Using default configuration..." << std::endl;
            server_config = Config::create_default_server_config();
        }
        
        // Create and configure the trading server
        std::cout << "Creating trading server..." << std::endl;
        g_server = TradingServerBuilder()
            .with_config(*server_config)
            .with_server_port(server_config->server_port)
            .with_strategy_config(server_config->strategy_config_file)
            .with_model_directory(server_config->model_directory)
            .with_risk_limits(server_config->global_risk_limits)
            .with_log_level(server_config->log_level)
            .build();
        
        if (!g_server) {
            std::cerr << "Failed to create trading server" << std::endl;
            return 1;
        }
        
        // Initialize the server
        std::cout << "Initializing server components..." << std::endl;
        auto init_future = g_server->initialize();
        if (!init_future.get()) {
            std::cerr << "Failed to initialize trading server" << std::endl;
            return 1;
        }
        
        // Load portfolios
        std::cout << "Loading portfolios..." << std::endl;
        auto portfolio_configs = Config::load_portfolio_configs("config/portfolios.json");
        for (const auto& config : portfolio_configs) {
            if (config.enabled) {
                auto create_future = g_server->create_portfolio(config);
                if (!create_future.get()) {
                    std::cerr << "Failed to create portfolio: " << config.id << std::endl;
                }
            }
        }
        
        // Load models
        std::cout << "Loading models..." << std::endl;
        auto model_configs = Config::load_model_configs("config/models.json");
        for (const auto& config : model_configs) {
            auto load_future = g_server->load_model(config);
            if (!load_future.get()) {
                std::cerr << "Failed to load model: " << config.id << std::endl;
            }
        }
        
        // Load strategies
        std::cout << "Loading strategies..." << std::endl;
        auto strategy_configs = Config::load_strategy_configs("config/strategies.json");
        for (const auto& config : strategy_configs) {
            if (config.get_parameter<bool>("enabled").value_or(false)) {
                auto load_future = g_server->load_strategy(config);
                if (!load_future.get()) {
                    std::cerr << "Failed to load strategy: " << config.id << std::endl;
                }
            }
        }
        
        // Start the server
        std::cout << "Starting trading server..." << std::endl;
        auto start_future = g_server->start();
        start_future.wait();
        
        print_status(*g_server);
        
        // Start performance monitoring in background
        std::thread monitor_thread(monitor_performance, std::cref(*g_server));
        
        // Main loop
        std::cout << "Trading server is running. Press Ctrl+C to stop." << std::endl;
        std::cout << "Commands: status, help, quit" << std::endl;
        
        std::string command;
        while (!g_shutdown_requested && std::getline(std::cin, command)) {
            if (command == "quit" || command == "exit") {
                break;
            } else if (command == "status") {
                print_status(*g_server);
            } else if (command == "help") {
                std::cout << "Available commands:" << std::endl;
                std::cout << "  status - Show server status" << std::endl;
                std::cout << "  help   - Show this help" << std::endl;
                std::cout << "  quit   - Shutdown server" << std::endl;
            } else if (!command.empty()) {
                std::cout << "Unknown command: " << command << std::endl;
                std::cout << "Type 'help' for available commands." << std::endl;
            }
        }
        
        // Shutdown
        std::cout << "Shutting down trading server..." << std::endl;
        g_shutdown_requested = true;
        
        if (monitor_thread.joinable()) {
            monitor_thread.join();
        }
        
        auto stop_future = g_server->stop();
        stop_future.wait();
        
        g_server->shutdown();
        g_server.reset();
        
        // Cleanup the trading system
        cleanup();
        
        std::cout << "Trading server shutdown complete." << std::endl;
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "Fatal error: " << e.what() << std::endl;
        
        if (g_server) {
            g_server->shutdown();
            g_server.reset();
        }
        
        cleanup();
        return 1;
    }
}
