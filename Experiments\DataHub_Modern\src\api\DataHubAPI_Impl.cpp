// DataHubAPI Implementation - Unified API Interface
#include "api/DataHubAPI.h"
#include <chrono>
#include <sstream>
#include <iomanip>
#include <regex>

namespace DataHub::API {

// DataHubAPI Implementation
DataHubAPI::DataHubAPI(std::shared_ptr<Services::IDataHubManager> manager)
    : manager_(std::move(manager)) {
    if (!manager_) {
        throw std::invalid_argument("DataHub manager cannot be null");
    }
}

// Quote Data API
APIResponse<Core::QuoteData> DataHubAPI::get_quote(const std::string& symbol) {
    if (!validate_symbol(symbol)) {
        return APIResponse<Core::QuoteData>::error_response("Invalid symbol format");
    }
    
    auto result = manager_->get_latest_quote(symbol);
    return handle_result(result);
}

APIResponse<std::vector<Core::QuoteData>> DataHubAPI::get_quotes(const std::vector<std::string>& symbols) {
    if (symbols.empty()) {
        return APIResponse<std::vector<Core::QuoteData>>::error_response("Symbol list cannot be empty");
    }
    
    auto quote_service = manager_->get_quote_service();
    if (!quote_service) {
        return APIResponse<std::vector<Core::QuoteData>>::error_response("Quote service not available");
    }
    
    Core::SymbolVector symbol_vector(symbols.begin(), symbols.end());
    auto result = quote_service->get_quotes(symbol_vector);
    return handle_result(result);
}

// Historical Data API
APIResponse<std::vector<Core::BarData>> DataHubAPI::get_bars(
    const std::string& symbol,
    const std::string& bar_size,
    const std::string& start_time,
    const std::string& end_time) {
    
    if (!validate_symbol(symbol)) {
        return APIResponse<std::vector<Core::BarData>>::error_response("Invalid symbol format");
    }
    
    // Parse bar size
    Core::BarSize parsed_bar_size;
    try {
        parsed_bar_size = parse_bar_size(bar_size);
    } catch (const std::exception& e) {
        return APIResponse<std::vector<Core::BarData>>::error_response("Invalid bar size: " + bar_size);
    }
    
    // Parse timestamps
    Core::Timestamp start_ts, end_ts;
    try {
        start_ts = parse_timestamp(start_time);
        end_ts = parse_timestamp(end_time);
    } catch (const std::exception& e) {
        return APIResponse<std::vector<Core::BarData>>::error_response("Invalid timestamp format");
    }
    
    auto result = manager_->get_history_bars(symbol, parsed_bar_size, start_ts, end_ts);
    return handle_result(result);
}

// Security Information API
APIResponse<Core::SecurityInfo> DataHubAPI::get_security(const std::string& symbol) {
    if (!validate_symbol(symbol)) {
        return APIResponse<Core::SecurityInfo>::error_response("Invalid symbol format");
    }
    
    auto result = manager_->get_security_info(symbol);
    return handle_result(result);
}

APIResponse<std::vector<Core::SecurityInfo>> DataHubAPI::search_securities(const std::string& query) {
    if (query.empty()) {
        return APIResponse<std::vector<Core::SecurityInfo>>::error_response("Search query cannot be empty");
    }
    
    auto security_service = manager_->get_security_service();
    if (!security_service) {
        return APIResponse<std::vector<Core::SecurityInfo>>::error_response("Security service not available");
    }
    
    Services::SecuritySearchCriteria criteria;
    criteria.symbol_pattern = query;
    criteria.limit = 100;
    
    auto result = security_service->search_securities(criteria);
    return handle_result(result);
}

// System API
APIResponse<bool> DataHubAPI::health_check() {
    auto result = manager_->health_check();
    return handle_result(result);
}

// Utility Methods
Core::Timestamp DataHubAPI::parse_timestamp(const std::string& time_str) {
    std::tm tm = {};
    std::istringstream ss(time_str);
    ss >> std::get_time(&tm, "%Y-%m-%dT%H:%M:%S");
    
    if (ss.fail()) {
        throw std::invalid_argument("Invalid timestamp format: " + time_str);
    }
    
    auto time_t = std::mktime(&tm);
    return std::chrono::system_clock::from_time_t(time_t);
}

std::string DataHubAPI::format_timestamp(const Core::Timestamp& timestamp) {
    auto time_t = std::chrono::system_clock::to_time_t(timestamp);
    auto tm = *std::localtime(&time_t);
    
    std::ostringstream ss;
    ss << std::put_time(&tm, "%Y-%m-%dT%H:%M:%S");
    return ss.str();
}

Core::BarSize DataHubAPI::parse_bar_size(const std::string& size_str) {
    if (size_str == "1m") return Core::BarSize::OneMinute;
    if (size_str == "5m") return Core::BarSize::FiveMinutes;
    if (size_str == "15m") return Core::BarSize::FifteenMinutes;
    if (size_str == "30m") return Core::BarSize::ThirtyMinutes;
    if (size_str == "1h") return Core::BarSize::OneHour;
    if (size_str == "1d") return Core::BarSize::OneDay;
    
    throw std::invalid_argument("Invalid bar size: " + size_str);
}

std::string DataHubAPI::format_bar_size(Core::BarSize size) {
    switch (size) {
        case Core::BarSize::OneMinute: return "1m";
        case Core::BarSize::FiveMinutes: return "5m";
        case Core::BarSize::FifteenMinutes: return "15m";
        case Core::BarSize::ThirtyMinutes: return "30m";
        case Core::BarSize::OneHour: return "1h";
        case Core::BarSize::OneDay: return "1d";
        default: return "unknown";
    }
}

// Private helper methods
template<typename T>
APIResponse<T> DataHubAPI::handle_result(const Core::Result<T>& result) {
    if (result.is_success()) {
        return APIResponse<T>::success_response(result.value());
    } else {
        return APIResponse<T>::error_response(result.error().message);
    }
}

bool DataHubAPI::validate_symbol(const std::string& symbol) const {
    if (symbol.empty() || symbol.length() > 20) {
        return false;
    }
    
    std::regex symbol_regex("^[A-Za-z0-9.]+$");
    return std::regex_match(symbol, symbol_regex);
}

} // namespace DataHub::API
