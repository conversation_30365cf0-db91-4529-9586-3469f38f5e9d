﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{2329C882-6FCE-3EBB-A391-E859C135CD3E}"
	ProjectSection(ProjectDependencies) = postProject
		{E845B234-6F99-32FB-9478-5DF9566AB4BB} = {E845B234-6F99-32FB-9478-5DF9566AB4BB}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{98AD73CA-F145-3AEB-9566-3FB65962A80A}"
	ProjectSection(ProjectDependencies) = postProject
		{2329C882-6FCE-3EBB-A391-E859C135CD3E} = {2329C882-6FCE-3EBB-A391-E859C135CD3E}
		{E845B234-6F99-32FB-9478-5DF9566AB4BB} = {E845B234-6F99-32FB-9478-5DF9566AB4BB}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "PACKAGE", "PACKAGE.vcxproj", "{D95A99A4-10EC-320C-A34A-9E22D0C5EACA}"
	ProjectSection(ProjectDependencies) = postProject
		{2329C882-6FCE-3EBB-A391-E859C135CD3E} = {2329C882-6FCE-3EBB-A391-E859C135CD3E}
		{E845B234-6F99-32FB-9478-5DF9566AB4BB} = {E845B234-6F99-32FB-9478-5DF9566AB4BB}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "RUN_TESTS", "RUN_TESTS.vcxproj", "{9BBEA8CB-F9F9-3E30-8271-51317AB222B3}"
	ProjectSection(ProjectDependencies) = postProject
		{E845B234-6F99-32FB-9478-5DF9566AB4BB} = {E845B234-6F99-32FB-9478-5DF9566AB4BB}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "..\..\\ZERO_CHECK.vcxproj", "{E845B234-6F99-32FB-9478-5DF9566AB4BB}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{2329C882-6FCE-3EBB-A391-E859C135CD3E}.Debug|x64.ActiveCfg = Debug|x64
		{2329C882-6FCE-3EBB-A391-E859C135CD3E}.Debug|x64.Build.0 = Debug|x64
		{2329C882-6FCE-3EBB-A391-E859C135CD3E}.Release|x64.ActiveCfg = Release|x64
		{2329C882-6FCE-3EBB-A391-E859C135CD3E}.Release|x64.Build.0 = Release|x64
		{2329C882-6FCE-3EBB-A391-E859C135CD3E}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{2329C882-6FCE-3EBB-A391-E859C135CD3E}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{2329C882-6FCE-3EBB-A391-E859C135CD3E}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{2329C882-6FCE-3EBB-A391-E859C135CD3E}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{98AD73CA-F145-3AEB-9566-3FB65962A80A}.Debug|x64.ActiveCfg = Debug|x64
		{98AD73CA-F145-3AEB-9566-3FB65962A80A}.Release|x64.ActiveCfg = Release|x64
		{98AD73CA-F145-3AEB-9566-3FB65962A80A}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{98AD73CA-F145-3AEB-9566-3FB65962A80A}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{D95A99A4-10EC-320C-A34A-9E22D0C5EACA}.Debug|x64.ActiveCfg = Debug|x64
		{D95A99A4-10EC-320C-A34A-9E22D0C5EACA}.Release|x64.ActiveCfg = Release|x64
		{D95A99A4-10EC-320C-A34A-9E22D0C5EACA}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{D95A99A4-10EC-320C-A34A-9E22D0C5EACA}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{9BBEA8CB-F9F9-3E30-8271-51317AB222B3}.Debug|x64.ActiveCfg = Debug|x64
		{9BBEA8CB-F9F9-3E30-8271-51317AB222B3}.Release|x64.ActiveCfg = Release|x64
		{9BBEA8CB-F9F9-3E30-8271-51317AB222B3}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{9BBEA8CB-F9F9-3E30-8271-51317AB222B3}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{E845B234-6F99-32FB-9478-5DF9566AB4BB}.Debug|x64.ActiveCfg = Debug|x64
		{E845B234-6F99-32FB-9478-5DF9566AB4BB}.Debug|x64.Build.0 = Debug|x64
		{E845B234-6F99-32FB-9478-5DF9566AB4BB}.Release|x64.ActiveCfg = Release|x64
		{E845B234-6F99-32FB-9478-5DF9566AB4BB}.Release|x64.Build.0 = Release|x64
		{E845B234-6F99-32FB-9478-5DF9566AB4BB}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{E845B234-6F99-32FB-9478-5DF9566AB4BB}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{E845B234-6F99-32FB-9478-5DF9566AB4BB}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{E845B234-6F99-32FB-9478-5DF9566AB4BB}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {F9B976AD-34CF-348B-A240-B624BFEA4F04}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
