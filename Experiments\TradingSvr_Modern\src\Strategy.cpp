/**
 * @file Strategy.cpp
 * @brief Implementation of Strategy base class and related components
 * <AUTHOR> Team
 * @date 2024
 */

#include "trading/Strategy.h"
#include "trading/Portfolio.h"
#include "trading/OrderManager.h"
#include "trading/DataProvider.h"
#include <iostream>
#include <algorithm>

namespace RoboQuant::Trading {

Strategy::Strategy(StrategyConfig config) : config_(std::move(config)) {}

StrategyState Strategy::get_state() const noexcept {
    return state_.load();
}

const StrategyConfig& Strategy::get_config() const noexcept {
    return config_;
}

bool Strategy::is_enabled() const noexcept {
    return enabled_.load();
}

void Strategy::enable() noexcept {
    enabled_.store(true);
}

void Strategy::disable() noexcept {
    enabled_.store(false);
}

PerformanceMetrics Strategy::get_performance() const {
    std::lock_guard lock(performance_mutex_);
    return performance_;
}

RiskLimits Strategy::get_risk_limits() const noexcept {
    std::shared_lock lock(risk_limits_mutex_);
    return risk_limits_;
}

void Strategy::set_risk_limits(const RiskLimits& limits) {
    std::unique_lock lock(risk_limits_mutex_);
    risk_limits_ = limits;
}

void Strategy::set_portfolio(SharedPtr<Portfolio> portfolio) {
    std::unique_lock lock(components_mutex_);
    portfolio_ = portfolio;
}

SharedPtr<Portfolio> Strategy::get_portfolio() const {
    std::shared_lock lock(components_mutex_);
    return portfolio_.lock();
}

void Strategy::set_order_manager(SharedPtr<OrderManager> order_manager) {
    std::unique_lock lock(components_mutex_);
    order_manager_ = order_manager;
}

SharedPtr<OrderManager> Strategy::get_order_manager() const {
    std::shared_lock lock(components_mutex_);
    return order_manager_.lock();
}

void Strategy::set_data_provider(SharedPtr<DataProvider> data_provider) {
    std::unique_lock lock(components_mutex_);
    data_provider_ = data_provider;
}

SharedPtr<DataProvider> Strategy::get_data_provider() const {
    std::shared_lock lock(components_mutex_);
    return data_provider_.lock();
}

bool Strategy::can_trade() const noexcept {
    return state_.load() == StrategyState::Running && enabled_.load();
}

bool Strategy::check_risk_limits(const OrderRequest& request) const {
    std::shared_lock lock(risk_limits_mutex_);
    
    // Check position value limit
    if (request.price.has_value()) {
        Amount position_value = static_cast<Amount>(request.quantity) * (*request.price);
        if (position_value > risk_limits_.max_position_value) {
            return false;
        }
    }
    
    // Additional risk checks can be added here
    return true;
}

void Strategy::log_info(const std::string& message) const {
    std::cout << "[INFO][" << config_.id << "] " << message << std::endl;
}

void Strategy::log_warning(const std::string& message) const {
    std::cout << "[WARN][" << config_.id << "] " << message << std::endl;
}

void Strategy::log_error(const std::string& message) const {
    std::cerr << "[ERROR][" << config_.id << "] " << message << std::endl;
}

void Strategy::set_state(StrategyState state) noexcept {
    state_.store(state);
}

// Strategy Registry Implementation
StrategyRegistry& StrategyRegistry::instance() {
    static StrategyRegistry instance;
    return instance;
}

void StrategyRegistry::register_factory(const std::string& name, UniquePtr<StrategyFactory> factory) {
    std::unique_lock lock(factories_mutex_);
    factories_[name] = std::move(factory);
}

UniquePtr<Strategy> StrategyRegistry::create_strategy(
    const std::string& strategy_type, 
    const StrategyConfig& config) const {
    
    std::shared_lock lock(factories_mutex_);
    auto it = factories_.find(strategy_type);
    
    if (it != factories_.end()) {
        return it->second->create_strategy(config);
    }
    
    return nullptr;
}

std::vector<std::string> StrategyRegistry::get_available_strategies() const {
    std::shared_lock lock(factories_mutex_);
    std::vector<std::string> result;
    result.reserve(factories_.size());
    
    for (const auto& [name, factory] : factories_) {
        auto supported = factory->get_supported_strategies();
        result.insert(result.end(), supported.begin(), supported.end());
    }
    
    return result;
}

// Strategy Manager Implementation
StrategyManager::~StrategyManager() {
    shutdown_all();
}

std::future<bool> StrategyManager::add_strategy(UniquePtr<Strategy> strategy) {
    std::promise<bool> promise;
    auto future = promise.get_future();
    
    if (!strategy) {
        promise.set_value(false);
        return future;
    }
    
    std::unique_lock lock(strategies_mutex_);
    auto id = strategy->get_config().id;
    
    if (strategies_.find(id) != strategies_.end()) {
        promise.set_value(false);
        return future;
    }
    
    strategies_[id] = std::move(strategy);
    promise.set_value(true);
    
    return future;
}

std::future<bool> StrategyManager::remove_strategy(const StrategyId& id) {
    std::promise<bool> promise;
    auto future = promise.get_future();
    
    std::unique_lock lock(strategies_mutex_);
    auto it = strategies_.find(id);
    
    if (it == strategies_.end()) {
        promise.set_value(false);
        return future;
    }
    
    // Stop strategy before removing
    auto stop_future = it->second->stop();
    stop_future.wait();
    
    strategies_.erase(it);
    promise.set_value(true);
    
    return future;
}

Strategy* StrategyManager::get_strategy(const StrategyId& id) const {
    std::shared_lock lock(strategies_mutex_);
    auto it = strategies_.find(id);
    return it != strategies_.end() ? it->second.get() : nullptr;
}

std::vector<Strategy*> StrategyManager::get_all_strategies() const {
    std::shared_lock lock(strategies_mutex_);
    std::vector<Strategy*> result;
    result.reserve(strategies_.size());
    
    for (const auto& [id, strategy] : strategies_) {
        result.push_back(strategy.get());
    }
    
    return result;
}

std::future<void> StrategyManager::start_all() {
    std::shared_lock lock(strategies_mutex_);
    std::vector<std::future<void>> futures;
    
    for (const auto& [id, strategy] : strategies_) {
        if (strategy->get_state() == StrategyState::Stopped) {
            futures.push_back(strategy->start());
        }
    }
    
    lock.unlock();
    
    // Wait for all strategies to start
    for (auto& future : futures) {
        future.wait();
    }
    
    std::promise<void> promise;
    promise.set_value();
    return promise.get_future();
}

std::future<void> StrategyManager::stop_all() {
    std::shared_lock lock(strategies_mutex_);
    std::vector<std::future<void>> futures;
    
    for (const auto& [id, strategy] : strategies_) {
        if (strategy->get_state() == StrategyState::Running) {
            futures.push_back(strategy->stop());
        }
    }
    
    lock.unlock();
    
    // Wait for all strategies to stop
    for (auto& future : futures) {
        future.wait();
    }
    
    std::promise<void> promise;
    promise.set_value();
    return promise.get_future();
}

void StrategyManager::shutdown_all() {
    std::unique_lock lock(strategies_mutex_);
    
    for (const auto& [id, strategy] : strategies_) {
        strategy->shutdown();
    }
    
    strategies_.clear();
}

void StrategyManager::distribute_market_data(const QuoteData& quote) {
    std::shared_lock lock(strategies_mutex_);
    
    for (const auto& [id, strategy] : strategies_) {
        if (strategy->can_trade()) {
            try {
                strategy->on_market_data(quote);
            } catch (const std::exception& e) {
                std::cerr << "Error in strategy " << id << " market data handler: " << e.what() << std::endl;
            }
        }
    }
}

void StrategyManager::distribute_bar_data(const BarData& bar) {
    std::shared_lock lock(strategies_mutex_);
    
    for (const auto& [id, strategy] : strategies_) {
        if (strategy->can_trade()) {
            try {
                strategy->on_bar_data(bar);
            } catch (const std::exception& e) {
                std::cerr << "Error in strategy " << id << " bar data handler: " << e.what() << std::endl;
            }
        }
    }
}

void StrategyManager::distribute_order_fill(const OrderFill& fill) {
    std::shared_lock lock(strategies_mutex_);
    
    for (const auto& [id, strategy] : strategies_) {
        if (strategy->can_trade()) {
            try {
                strategy->on_order_fill(fill);
            } catch (const std::exception& e) {
                std::cerr << "Error in strategy " << id << " order fill handler: " << e.what() << std::endl;
            }
        }
    }
}

void StrategyManager::distribute_order_update(const Order& order) {
    std::shared_lock lock(strategies_mutex_);
    
    for (const auto& [id, strategy] : strategies_) {
        if (strategy->can_trade()) {
            try {
                strategy->on_order_update(order);
            } catch (const std::exception& e) {
                std::cerr << "Error in strategy " << id << " order update handler: " << e.what() << std::endl;
            }
        }
    }
}

} // namespace RoboQuant::Trading
