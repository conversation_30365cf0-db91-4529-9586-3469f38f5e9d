#----------------------------------------------------------------
# Generated CMake target import file for configuration "Release".
#----------------------------------------------------------------

# Commands may need to know the format version.
set(CMAKE_IMPORT_FILE_VERSION 1)

# Import target "DataHub::datahub" for configuration "Release"
set_property(TARGET DataHub::datahub APPEND PROPERTY IMPORTED_CONFIGURATIONS RELEASE)
set_target_properties(DataHub::datahub PROPERTIES
  IMPORTED_LOCATION_RELEASE "${_IMPORT_PREFIX}/lib/datahub.lib"
  )

list(APPEND _cmake_import_check_targets DataHub::datahub )
list(APPEND _cmake_import_check_files_for_DataHub::datahub "${_IMPORT_PREFIX}/lib/datahub.lib" )

# Import target "DataHub::datahub_core" for configuration "Release"
set_property(TARGET DataHub::datahub_core APPEND PROPERTY IMPORTED_CONFIGURATIONS RELEASE)
set_target_properties(DataHub::datahub_core PROPERTIES
  IMPORTED_LINK_INTERFACE_LANGUAGES_RELEASE "CXX"
  IMPORTED_LOCATION_RELEASE "${_IMPORT_PREFIX}/lib/datahub_core.lib"
  )

list(APPEND _cmake_import_check_targets DataHub::datahub_core )
list(APPEND _cmake_import_check_files_for_DataHub::datahub_core "${_IMPORT_PREFIX}/lib/datahub_core.lib" )

# Import target "DataHub::datahub_data" for configuration "Release"
set_property(TARGET DataHub::datahub_data APPEND PROPERTY IMPORTED_CONFIGURATIONS RELEASE)
set_target_properties(DataHub::datahub_data PROPERTIES
  IMPORTED_LINK_INTERFACE_LANGUAGES_RELEASE "CXX"
  IMPORTED_LOCATION_RELEASE "${_IMPORT_PREFIX}/lib/datahub_data.lib"
  )

list(APPEND _cmake_import_check_targets DataHub::datahub_data )
list(APPEND _cmake_import_check_files_for_DataHub::datahub_data "${_IMPORT_PREFIX}/lib/datahub_data.lib" )

# Import target "DataHub::datahub_services" for configuration "Release"
set_property(TARGET DataHub::datahub_services APPEND PROPERTY IMPORTED_CONFIGURATIONS RELEASE)
set_target_properties(DataHub::datahub_services PROPERTIES
  IMPORTED_LINK_INTERFACE_LANGUAGES_RELEASE "CXX"
  IMPORTED_LOCATION_RELEASE "${_IMPORT_PREFIX}/lib/datahub_services.lib"
  )

list(APPEND _cmake_import_check_targets DataHub::datahub_services )
list(APPEND _cmake_import_check_files_for_DataHub::datahub_services "${_IMPORT_PREFIX}/lib/datahub_services.lib" )

# Import target "DataHub::datahub_api" for configuration "Release"
set_property(TARGET DataHub::datahub_api APPEND PROPERTY IMPORTED_CONFIGURATIONS RELEASE)
set_target_properties(DataHub::datahub_api PROPERTIES
  IMPORTED_LINK_INTERFACE_LANGUAGES_RELEASE "CXX"
  IMPORTED_LOCATION_RELEASE "${_IMPORT_PREFIX}/lib/datahub_api.lib"
  )

list(APPEND _cmake_import_check_targets DataHub::datahub_api )
list(APPEND _cmake_import_check_files_for_DataHub::datahub_api "${_IMPORT_PREFIX}/lib/datahub_api.lib" )

# Commands beyond this point should not need to know the version.
set(CMAKE_IMPORT_FILE_VERSION)
