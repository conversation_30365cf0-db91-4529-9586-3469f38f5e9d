/**
 * @file Portfolio.h
 * @brief Modern portfolio management system
 * <AUTHOR> Team
 * @date 2024
 */

#pragma once

#include "Types.h"
#include "Position.h"
#include <unordered_map>
#include <shared_mutex>
#include <functional>

namespace RoboQuant::Trading {

/**
 * @brief Portfolio configuration
 */
struct PortfolioConfig {
    PortfolioId id;
    std::string name;
    std::string description;
    Amount initial_capital{0.0};
    std::string base_currency{"USD"};
    RiskLimits risk_limits;
    bool enabled{true};
};

/**
 * @brief Portfolio snapshot for performance tracking
 */
struct PortfolioSnapshot {
    TimePoint timestamp;
    Amount total_value{0.0};
    Amount cash{0.0};
    Amount unrealized_pnl{0.0};
    Amount realized_pnl{0.0};
    std::unordered_map<AssetId, Position> positions;
};

/**
 * @brief Modern portfolio implementation
 */
class Portfolio {
public:
    explicit Portfolio(PortfolioConfig config);
    ~Portfolio() = default;

    // Non-copyable, movable
    Portfolio(const Portfolio&) = delete;
    Portfolio& operator=(const Portfolio&) = delete;
    Portfolio(Portfolio&&) = default;
    Portfolio& operator=(Portfolio&&) = default;

    // Configuration access
    [[nodiscard]] const PortfolioConfig& get_config() const noexcept;
    [[nodiscard]] PortfolioId get_id() const noexcept;
    [[nodiscard]] const std::string& get_name() const noexcept;

    // Cash management
    [[nodiscard]] Amount get_cash() const;
    void add_cash(Amount amount);
    void withdraw_cash(Amount amount);
    [[nodiscard]] bool has_sufficient_cash(Amount required) const;

    // Position management
    [[nodiscard]] std::optional<Position> get_position(const AssetId& asset_id) const;
    [[nodiscard]] std::vector<Position> get_all_positions() const;
    [[nodiscard]] bool has_position(const AssetId& asset_id) const;
    
    void update_position(const AssetId& asset_id, Quantity quantity, Price price, Amount commission = 0.0);
    void close_position(const AssetId& asset_id, Price price, Amount commission = 0.0);
    void close_all_positions(const std::function<Price(const AssetId&)>& price_provider);

    // Portfolio valuation
    [[nodiscard]] Amount get_total_value(const std::function<Price(const AssetId&)>& price_provider) const;
    [[nodiscard]] Amount get_unrealized_pnl(const std::function<Price(const AssetId&)>& price_provider) const;
    [[nodiscard]] Amount get_realized_pnl() const;
    
    // Performance metrics
    [[nodiscard]] PerformanceMetrics calculate_performance(
        const std::function<Price(const AssetId&)>& price_provider
    ) const;
    
    // Risk management
    [[nodiscard]] const RiskLimits& get_risk_limits() const;
    void set_risk_limits(const RiskLimits& limits);
    [[nodiscard]] bool check_risk_limits(const OrderRequest& request, Price current_price) const;

    // Snapshots and history
    [[nodiscard]] PortfolioSnapshot create_snapshot(
        const std::function<Price(const AssetId&)>& price_provider
    ) const;
    
    void add_snapshot(const PortfolioSnapshot& snapshot);
    [[nodiscard]] std::vector<PortfolioSnapshot> get_snapshots(
        std::optional<TimePoint> start_time = std::nullopt,
        std::optional<TimePoint> end_time = std::nullopt
    ) const;

    // Event callbacks
    using PositionUpdateCallback = std::function<void(const AssetId&, const Position&)>;
    using CashUpdateCallback = std::function<void(Amount new_cash, Amount change)>;
    
    void set_position_update_callback(PositionUpdateCallback callback);
    void set_cash_update_callback(CashUpdateCallback callback);

    // State management
    [[nodiscard]] bool is_enabled() const noexcept;
    void enable() noexcept;
    void disable() noexcept;

    // Serialization support
    [[nodiscard]] std::string to_json() const;
    static std::optional<Portfolio> from_json(const std::string& json);

private:
    void notify_position_update(const AssetId& asset_id, const Position& position);
    void notify_cash_update(Amount new_cash, Amount change);
    
    PortfolioConfig config_;
    std::atomic<bool> enabled_{true};
    
    mutable std::shared_mutex cash_mutex_;
    Amount cash_;
    Amount realized_pnl_{0.0};
    
    mutable std::shared_mutex positions_mutex_;
    std::unordered_map<AssetId, Position> positions_;
    
    mutable std::shared_mutex risk_limits_mutex_;
    RiskLimits risk_limits_;
    
    mutable std::shared_mutex snapshots_mutex_;
    std::vector<PortfolioSnapshot> snapshots_;
    
    // Callbacks
    std::mutex callbacks_mutex_;
    PositionUpdateCallback position_callback_;
    CashUpdateCallback cash_callback_;
};

/**
 * @brief Portfolio manager for handling multiple portfolios
 */
class PortfolioManager {
public:
    PortfolioManager() = default;
    ~PortfolioManager() = default;

    // Non-copyable, movable
    PortfolioManager(const PortfolioManager&) = delete;
    PortfolioManager& operator=(const PortfolioManager&) = delete;
    PortfolioManager(PortfolioManager&&) = default;
    PortfolioManager& operator=(PortfolioManager&&) = default;

    // Portfolio management
    bool add_portfolio(UniquePtr<Portfolio> portfolio);
    bool remove_portfolio(const PortfolioId& id);
    
    [[nodiscard]] Portfolio* get_portfolio(const PortfolioId& id) const;
    [[nodiscard]] std::vector<Portfolio*> get_all_portfolios() const;
    [[nodiscard]] std::vector<PortfolioId> get_portfolio_ids() const;
    
    [[nodiscard]] bool has_portfolio(const PortfolioId& id) const;
    [[nodiscard]] size_t portfolio_count() const;

    // Batch operations
    void update_all_snapshots(const std::function<Price(const AssetId&)>& price_provider);
    [[nodiscard]] Amount get_total_value(const std::function<Price(const AssetId&)>& price_provider) const;
    
    // Risk management
    [[nodiscard]] bool check_global_risk_limits(const OrderRequest& request, Price current_price) const;
    void set_global_risk_limits(const RiskLimits& limits);
    [[nodiscard]] const RiskLimits& get_global_risk_limits() const;

    // Event callbacks
    using PortfolioEventCallback = std::function<void(const PortfolioId&, const std::string& event)>;
    void set_portfolio_event_callback(PortfolioEventCallback callback);

    // Persistence
    bool save_all_portfolios(const std::string& directory) const;
    bool load_all_portfolios(const std::string& directory);

private:
    mutable std::shared_mutex portfolios_mutex_;
    std::unordered_map<PortfolioId, UniquePtr<Portfolio>> portfolios_;
    
    mutable std::shared_mutex global_risk_mutex_;
    RiskLimits global_risk_limits_;
    
    std::mutex callback_mutex_;
    PortfolioEventCallback portfolio_event_callback_;
};

} // namespace RoboQuant::Trading
