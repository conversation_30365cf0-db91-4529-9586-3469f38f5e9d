﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{E7736C9E-7A39-3258-806C-AC988CF7BA56}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>catch2-populate</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\CMakeFiles\a6d45bd166179965c26f64c43147948f\catch2-populate-mkdir.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Creating directories for 'catch2-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"D:\Program Files\CMake\bin\cmake.exe" -Dcfgdir=/Debug -P E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/catch2-subbuild/catch2-populate-prefix/tmp/catch2-populate-mkdirs.cmake
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E touch E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/catch2-subbuild/catch2-populate-prefix/src/catch2-populate-stamp/Debug/catch2-populate-mkdir
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\catch2-populate-prefix\src\catch2-populate-stamp\Debug\catch2-populate-mkdir</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\CMakeFiles\a6d45bd166179965c26f64c43147948f\catch2-populate-download.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Performing download step (git clone) for 'catch2-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
cd E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -P E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/catch2-subbuild/catch2-populate-prefix/tmp/catch2-populate-gitclone.cmake
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E touch E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/catch2-subbuild/catch2-populate-prefix/src/catch2-populate-stamp/Debug/catch2-populate-download
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\catch2-populate-prefix\src\catch2-populate-stamp\catch2-populate-gitinfo.txt;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\catch2-populate-prefix\src\catch2-populate-stamp\Debug\catch2-populate-mkdir;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\catch2-populate-prefix\src\catch2-populate-stamp\Debug\catch2-populate-download</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\CMakeFiles\a6d45bd166179965c26f64c43147948f\catch2-populate-update.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Performing update step for 'catch2-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
cd E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -P E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/catch2-subbuild/catch2-populate-prefix/tmp/catch2-populate-gitupdate.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\catch2-populate-prefix\src\catch2-populate-stamp\Debug\catch2-populate-download;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\catch2-populate-prefix\src\catch2-populate-stamp\Debug\catch2-populate-update</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</VerifyInputsAndOutputsExist>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\CMakeFiles\a6d45bd166179965c26f64c43147948f\catch2-populate-patch.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">No patch step for 'catch2-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"D:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E touch E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/catch2-subbuild/catch2-populate-prefix/src/catch2-populate-stamp/Debug/catch2-populate-patch
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\catch2-populate-prefix\src\catch2-populate-stamp\Debug\catch2-populate-update;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\catch2-populate-prefix\src\catch2-populate-stamp\Debug\catch2-populate-patch</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</VerifyInputsAndOutputsExist>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\CMakeFiles\a6d45bd166179965c26f64c43147948f\catch2-populate-configure.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">No configure step for 'catch2-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
cd E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-build
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E touch E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/catch2-subbuild/catch2-populate-prefix/src/catch2-populate-stamp/Debug/catch2-populate-configure
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\catch2-populate-prefix\tmp\catch2-populate-cfgcmd.txt;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\catch2-populate-prefix\src\catch2-populate-stamp\Debug\catch2-populate-patch;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\catch2-populate-prefix\src\catch2-populate-stamp\Debug\catch2-populate-configure</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\CMakeFiles\a6d45bd166179965c26f64c43147948f\catch2-populate-build.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">No build step for 'catch2-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
cd E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-build
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E touch E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/catch2-subbuild/catch2-populate-prefix/src/catch2-populate-stamp/Debug/catch2-populate-build
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\catch2-populate-prefix\src\catch2-populate-stamp\Debug\catch2-populate-configure;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\catch2-populate-prefix\src\catch2-populate-stamp\Debug\catch2-populate-build</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\CMakeFiles\a6d45bd166179965c26f64c43147948f\catch2-populate-install.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">No install step for 'catch2-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
cd E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-build
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E touch E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/catch2-subbuild/catch2-populate-prefix/src/catch2-populate-stamp/Debug/catch2-populate-install
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\catch2-populate-prefix\src\catch2-populate-stamp\Debug\catch2-populate-build;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\catch2-populate-prefix\src\catch2-populate-stamp\Debug\catch2-populate-install</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\CMakeFiles\a6d45bd166179965c26f64c43147948f\catch2-populate-test.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">No test step for 'catch2-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
cd E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-build
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E touch E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/catch2-subbuild/catch2-populate-prefix/src/catch2-populate-stamp/Debug/catch2-populate-test
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\catch2-populate-prefix\src\catch2-populate-stamp\Debug\catch2-populate-install;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\catch2-populate-prefix\src\catch2-populate-stamp\Debug\catch2-populate-test</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\CMakeFiles\b7b71d606afd8b5d90c9dfd1a7387e4f\catch2-populate-complete.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Completed 'catch2-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"D:\Program Files\CMake\bin\cmake.exe" -E make_directory E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/catch2-subbuild/CMakeFiles/Debug
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E touch E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/catch2-subbuild/CMakeFiles/Debug/catch2-populate-complete
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E touch E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/catch2-subbuild/catch2-populate-prefix/src/catch2-populate-stamp/Debug/catch2-populate-done
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\catch2-populate-prefix\src\catch2-populate-stamp\Debug\catch2-populate-install;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\catch2-populate-prefix\src\catch2-populate-stamp\Debug\catch2-populate-mkdir;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\catch2-populate-prefix\src\catch2-populate-stamp\Debug\catch2-populate-download;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\catch2-populate-prefix\src\catch2-populate-stamp\Debug\catch2-populate-update;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\catch2-populate-prefix\src\catch2-populate-stamp\Debug\catch2-populate-patch;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\catch2-populate-prefix\src\catch2-populate-stamp\Debug\catch2-populate-configure;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\catch2-populate-prefix\src\catch2-populate-stamp\Debug\catch2-populate-build;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\catch2-populate-prefix\src\catch2-populate-stamp\Debug\catch2-populate-test;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\CMakeFiles\Debug\catch2-populate-complete</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</VerifyInputsAndOutputsExist>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\CMakeFiles\c2482a8fe812196cee829a335fd55e97\catch2-populate.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\CMakeFiles\Debug\catch2-populate-complete;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\CMakeFiles\catch2-populate</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</VerifyInputsAndOutputsExist>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/catch2-subbuild/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"D:\Program Files\CMake\bin\cmake.exe" -SE:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/catch2-subbuild -BE:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/catch2-subbuild --check-stamp-file E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/catch2-subbuild/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeGenericSystem.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeInitializeConfigs.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeSystemSpecificInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeSystemSpecificInitialize.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\ExternalProject.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\ExternalProject\RepositoryInfo.txt.in;D:\Program Files\CMake\share\cmake-3.25\Modules\ExternalProject\cfgcmd.txt.in;D:\Program Files\CMake\share\cmake-3.25\Modules\ExternalProject\gitclone.cmake.in;D:\Program Files\CMake\share\cmake-3.25\Modules\ExternalProject\gitupdate.cmake.in;D:\Program Files\CMake\share\cmake-3.25\Modules\ExternalProject\mkdirs.cmake.in;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\WindowsPaths.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\CMakeFiles\3.25.0-rc2\CMakeSystem.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\catch2-populate-prefix\tmp\catch2-populate-mkdirs.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <None Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\CMakeFiles\catch2-populate">
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\ZERO_CHECK.vcxproj">
      <Project>{D9D59877-62EA-3F21-A104-AE7471E4A128}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>