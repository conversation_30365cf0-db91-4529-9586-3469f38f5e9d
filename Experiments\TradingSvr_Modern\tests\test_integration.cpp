/**
 * @file test_integration.cpp
 * @brief Integration tests for the trading system
 * <AUTHOR> Team
 * @date 2024
 */

#include <gtest/gtest.h>
#include "trading/Trading.h"
#include <thread>
#include <chrono>

using namespace RoboQuant::Trading;

class IntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Initialize the trading system
        initialize();
        
        // Create test configuration
        config_ = Config::create_default_server_config();
        config_.server_port = 18080; // Use different port for testing
        config_.enable_web_interface = false; // Disable for testing
        config_.enable_api_server = false;
        
        // Create server
        server_ = TradingServerBuilder()
            .with_config(config_)
            .build();
        
        ASSERT_NE(server_, nullptr);
    }
    
    void TearDown() override {
        if (server_ && server_->is_running()) {
            auto stop_future = server_->stop();
            stop_future.wait();
        }
        server_.reset();
        cleanup();
    }

    TradingServerConfig config_;
    std::unique_ptr<TradingServer> server_;
};

TEST_F(IntegrationTest, ServerLifecycle) {
    // Test initialization
    auto init_future = server_->initialize();
    EXPECT_TRUE(init_future.get());
    
    // Test start
    auto start_future = server_->start();
    start_future.wait();
    EXPECT_TRUE(server_->is_running());
    EXPECT_EQ(server_->get_status(), TradingServerStatus::Running);
    
    // Test stop
    auto stop_future = server_->stop();
    stop_future.wait();
    EXPECT_FALSE(server_->is_running());
    EXPECT_EQ(server_->get_status(), TradingServerStatus::Stopped);
    
    // Test shutdown
    server_->shutdown();
    EXPECT_EQ(server_->get_status(), TradingServerStatus::Stopped);
}

TEST_F(IntegrationTest, PortfolioManagement) {
    // Initialize server
    auto init_future = server_->initialize();
    ASSERT_TRUE(init_future.get());
    
    // Create portfolio
    PortfolioConfig portfolio_config;
    portfolio_config.id = "test_portfolio_integration";
    portfolio_config.name = "Test Portfolio Integration";
    portfolio_config.initial_capital = 1000000.0;
    portfolio_config.base_currency = "USD";
    portfolio_config.enabled = true;
    
    auto create_future = server_->create_portfolio(portfolio_config);
    EXPECT_TRUE(create_future.get());
    
    // Verify portfolio exists
    auto portfolio_ids = server_->get_portfolio_ids();
    EXPECT_EQ(portfolio_ids.size(), 1);
    EXPECT_EQ(portfolio_ids[0], "test_portfolio_integration");
    
    // Get portfolio from manager
    auto* portfolio = server_->get_portfolio_manager().get_portfolio("test_portfolio_integration");
    ASSERT_NE(portfolio, nullptr);
    EXPECT_EQ(portfolio->get_cash(), 1000000.0);
    
    // Remove portfolio
    auto remove_future = server_->remove_portfolio("test_portfolio_integration");
    EXPECT_TRUE(remove_future.get());
    
    // Verify removal
    portfolio_ids = server_->get_portfolio_ids();
    EXPECT_EQ(portfolio_ids.size(), 0);
}

TEST_F(IntegrationTest, StrategyManagement) {
    // Initialize server
    auto init_future = server_->initialize();
    ASSERT_TRUE(init_future.get());
    
    // Create strategy configuration
    StrategyConfig strategy_config;
    strategy_config.id = "test_strategy_integration";
    strategy_config.name = "Test Strategy Integration";
    strategy_config.description = "Integration test strategy";
    strategy_config.enabled = true;
    strategy_config.parameters["entry_threshold"] = 0.02;
    strategy_config.parameters["stop_loss_pct"] = 0.05;
    
    // Load strategy
    auto load_future = server_->load_strategy(strategy_config);
    // Note: This might fail if the strategy type is not registered
    // EXPECT_TRUE(load_future.get());
    
    // For now, just verify the strategy manager exists
    auto& strategy_manager = server_->get_strategy_manager();
    EXPECT_EQ(strategy_manager.get_all_strategies().size(), 0); // No strategies loaded yet
}

TEST_F(IntegrationTest, ModelManagement) {
    // Initialize server
    auto init_future = server_->initialize();
    ASSERT_TRUE(init_future.get());
    
    // Create model configuration
    ModelConfig model_config;
    model_config.id = "test_model_integration";
    model_config.name = "Test Model Integration";
    model_config.type = ModelType::LightGBM;
    model_config.model_path = "test_model.txt"; // Non-existent file
    
    // Try to load model (should fail due to missing file)
    auto load_future = server_->load_model(model_config);
    EXPECT_FALSE(load_future.get()); // Should fail
    
    // Verify no models loaded
    auto loaded_models = server_->get_loaded_models();
    EXPECT_EQ(loaded_models.size(), 0);
}

TEST_F(IntegrationTest, EventSystem) {
    // Initialize server
    auto init_future = server_->initialize();
    ASSERT_TRUE(init_future.get());
    
    auto start_future = server_->start();
    start_future.wait();
    
    // Get event bus
    auto& event_bus = server_->get_event_bus();
    EXPECT_TRUE(event_bus.is_running());
    
    // Test event publishing
    QuoteData quote;
    quote.asset_id = "AAPL";
    quote.timestamp = std::chrono::system_clock::now();
    quote.last_price = 150.0;
    quote.bid_price = 149.95;
    quote.ask_price = 150.05;
    
    // Publish event (should not throw)
    EXPECT_NO_THROW(event_bus.publish<MarketDataEvent>(quote));
    
    // Give some time for event processing
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
}

TEST_F(IntegrationTest, NetworkManager) {
    // Initialize server
    auto init_future = server_->initialize();
    ASSERT_TRUE(init_future.get());
    
    auto start_future = server_->start();
    start_future.wait();
    
    // Get network manager
    auto& network_manager = server_->get_network_manager();
    EXPECT_TRUE(network_manager.is_running());
    
    // Test HTTP client
    auto& http_client = network_manager.get_http_client();
    
    // Test cloud client
    auto& cloud_client = network_manager.get_cloud_client();
    
    // Test server
    auto& network_server = network_manager.get_server();
    EXPECT_TRUE(network_server.is_running());
}

TEST_F(IntegrationTest, ExpressionEngine) {
    // Initialize server
    auto init_future = server_->initialize();
    ASSERT_TRUE(init_future.get());
    
    // Get expression manager
    auto& expr_manager = server_->get_expression_manager();
    
    // Test simple expression evaluation
    ExpressionContext context;
    context.set_double("price", 150.0);
    context.set_double("threshold", 0.02);
    
    auto result = expr_manager.evaluate("price * (1 + threshold)", context);
    EXPECT_TRUE(std::holds_alternative<double>(result));
    
    auto value = std::get<double>(result);
    EXPECT_NEAR(value, 153.0, 0.001); // 150 * 1.02 = 153
    
    // Test boolean expression
    context.set_double("signal", 0.025);
    auto bool_result = expr_manager.evaluate_as<bool>("signal > threshold", context);
    ASSERT_TRUE(bool_result.has_value());
    EXPECT_TRUE(*bool_result); // 0.025 > 0.02
}

TEST_F(IntegrationTest, RiskManagement) {
    // Initialize server
    auto init_future = server_->initialize();
    ASSERT_TRUE(init_future.get());
    
    // Set global risk limits
    RiskLimits global_limits;
    global_limits.max_position_value = 500000.0;
    global_limits.max_daily_loss = 50000.0;
    global_limits.max_leverage = 2.0;
    
    server_->set_global_risk_limits(global_limits);
    
    auto retrieved_limits = server_->get_global_risk_limits();
    EXPECT_EQ(retrieved_limits.max_position_value, 500000.0);
    EXPECT_EQ(retrieved_limits.max_daily_loss, 50000.0);
    EXPECT_EQ(retrieved_limits.max_leverage, 2.0);
    
    // Test risk check
    OrderRequest request;
    request.asset_id = "AAPL";
    request.side = Side::Buy;
    request.quantity = 1000;
    request.type = OrderType::Market;
    
    // Should pass risk check (1000 * 150 = 150,000 < 500,000)
    EXPECT_TRUE(server_->check_global_risk(request));
    
    // Should fail risk check (10000 * 150 = 1,500,000 > 500,000)
    request.quantity = 10000;
    EXPECT_FALSE(server_->check_global_risk(request));
}

TEST_F(IntegrationTest, PerformanceMonitoring) {
    // Initialize server
    auto init_future = server_->initialize();
    ASSERT_TRUE(init_future.get());
    
    auto start_future = server_->start();
    start_future.wait();
    
    // Get performance metrics
    auto overall_perf = server_->get_overall_performance();
    EXPECT_EQ(overall_perf.total_return, 0.0); // No trading yet
    EXPECT_EQ(overall_perf.total_trades, 0);
    
    auto strategy_perf = server_->get_strategy_performance();
    EXPECT_EQ(strategy_perf.size(), 0); // No strategies loaded
    
    auto portfolio_perf = server_->get_portfolio_performance();
    EXPECT_EQ(portfolio_perf.size(), 0); // No portfolios created
}

TEST_F(IntegrationTest, ConfigurationPersistence) {
    // Test configuration loading and saving
    auto test_config = Config::create_default_server_config();
    test_config.server_id = "test_server_persistence";
    test_config.server_port = 19080;
    
    // Save configuration
    std::string config_file = "test_server_config.json";
    EXPECT_TRUE(Config::save_server_config(test_config, config_file));
    
    // Load configuration
    auto loaded_config = Config::load_server_config(config_file);
    ASSERT_TRUE(loaded_config.has_value());
    EXPECT_EQ(loaded_config->server_id, "test_server_persistence");
    EXPECT_EQ(loaded_config->server_port, 19080);
    
    // Clean up
    std::remove(config_file.c_str());
}

// Stress test
TEST_F(IntegrationTest, StressTest) {
    // Initialize server
    auto init_future = server_->initialize();
    ASSERT_TRUE(init_future.get());
    
    auto start_future = server_->start();
    start_future.wait();
    
    // Create multiple portfolios
    const int num_portfolios = 10;
    for (int i = 0; i < num_portfolios; ++i) {
        PortfolioConfig config;
        config.id = "stress_portfolio_" + std::to_string(i);
        config.name = "Stress Portfolio " + std::to_string(i);
        config.initial_capital = 100000.0;
        
        auto create_future = server_->create_portfolio(config);
        EXPECT_TRUE(create_future.get());
    }
    
    // Verify all portfolios created
    auto portfolio_ids = server_->get_portfolio_ids();
    EXPECT_EQ(portfolio_ids.size(), num_portfolios);
    
    // Generate many events
    auto& event_bus = server_->get_event_bus();
    const int num_events = 1000;
    
    for (int i = 0; i < num_events; ++i) {
        QuoteData quote;
        quote.asset_id = "STRESS_" + std::to_string(i % 10);
        quote.timestamp = std::chrono::system_clock::now();
        quote.last_price = 100.0 + (i % 100);
        
        event_bus.publish<MarketDataEvent>(quote);
    }
    
    // Give time for event processing
    std::this_thread::sleep_for(std::chrono::seconds(1));
    
    // Server should still be running
    EXPECT_TRUE(server_->is_running());
}
