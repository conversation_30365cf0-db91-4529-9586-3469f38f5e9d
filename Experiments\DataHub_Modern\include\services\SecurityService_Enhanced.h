#pragma once

#include "../core/Types.h"
#include "../core/SecurityInfo.h"
#include "../data/IDataRepository_Simple.h"
#include <memory>
#include <vector>
#include <unordered_map>
#include <unordered_set>
#include <functional>
#include <string>
#include <regex>

namespace DataHub::Services {

// Security service configuration
struct SecurityServiceConfig {
    // Data update settings
    bool enable_auto_update{true};
    std::uint32_t update_interval_hours{24};
    
    // Cache settings
    std::size_t max_cache_size{50000};
    std::uint32_t cache_expire_hours{12};
    
    // Data sources
    std::vector<std::string> data_sources{"default"};
    std::uint32_t source_timeout_seconds{30};
    
    // Validation settings
    bool enable_strict_validation{true};
    bool auto_fix_data{false};
};

// Security search criteria
struct SecuritySearchCriteria {
    std::optional<std::string> symbol_pattern;
    std::optional<std::string> name_pattern;
    std::optional<Core::SecurityType> security_type;
    std::optional<Core::Market> market;
    std::optional<std::string> sector;
    std::optional<std::string> industry;
    std::optional<Core::Currency> currency;
    std::optional<bool> is_active;
    
    // Price range filters
    std::optional<Core::Price> min_price;
    std::optional<Core::Price> max_price;
    
    // Market cap filters
    std::optional<double> min_market_cap;
    std::optional<double> max_market_cap;
    
    // Sorting and pagination
    enum class SortField {
        Symbol,
        Name,
        MarketCap,
        Price,
        Volume
    };
    
    std::optional<SortField> sort_field;
    bool sort_ascending{true};
    std::optional<std::size_t> limit;
    std::optional<std::size_t> offset;
};

// Security statistics
struct SecurityStatistics {
    Core::Symbol symbol;
    std::size_t total_securities{0};
    std::size_t active_securities{0};
    std::unordered_map<Core::SecurityType, std::size_t> type_counts;
    std::unordered_map<Core::Market, std::size_t> market_counts;
    std::unordered_map<std::string, std::size_t> sector_counts;
    Core::Timestamp last_update;
};

// Security service interface
class ISecurityService {
public:
    virtual ~ISecurityService() = default;
    
    // Service lifecycle
    virtual Core::Result<void> start() = 0;
    virtual Core::Result<void> stop() = 0;
    virtual bool is_running() const noexcept = 0;
    
    // Security information operations
    virtual Core::Result<Core::SecurityInfo> get_security(const Core::Symbol& symbol) = 0;
    virtual Core::Result<Core::SecurityInfoVector> get_securities(const Core::SymbolVector& symbols) = 0;
    virtual Core::Result<Core::SecurityInfoVector> search_securities(const SecuritySearchCriteria& criteria) = 0;
    
    virtual Core::Result<void> add_security(const Core::SecurityInfo& security) = 0;
    virtual Core::Result<void> update_security(const Core::SecurityInfo& security) = 0;
    virtual Core::Result<void> remove_security(const Core::Symbol& symbol) = 0;
    
    // Block/Sector operations
    virtual Core::Result<Core::BlockInfo> get_block(const std::string& name) = 0;
    virtual Core::Result<Core::BlockInfoVector> get_all_blocks() = 0;
    virtual Core::Result<Core::SymbolVector> get_block_securities(const std::string& block_name) = 0;
    
    virtual Core::Result<void> add_block(const Core::BlockInfo& block) = 0;
    virtual Core::Result<void> update_block(const Core::BlockInfo& block) = 0;
    virtual Core::Result<void> remove_block(const std::string& name) = 0;
    
    virtual Core::Result<void> add_security_to_block(const Core::Symbol& symbol, const std::string& block_name) = 0;
    virtual Core::Result<void> remove_security_from_block(const Core::Symbol& symbol, const std::string& block_name) = 0;
    
    // Bulk operations
    virtual Core::Result<void> bulk_import(const Core::SecurityInfoVector& securities) = 0;
    virtual Core::Result<Core::SecurityInfoVector> bulk_export(const Core::SymbolVector& symbols) = 0;
    
    // Data synchronization
    virtual Core::Result<void> sync_from_source(const std::string& source_name) = 0;
    virtual Core::Result<void> sync_all_sources() = 0;
    
    // Statistics and analytics
    virtual Core::Result<SecurityStatistics> get_statistics() = 0;
    virtual Core::Result<std::size_t> get_security_count() = 0;
    virtual Core::Result<Core::SymbolVector> get_active_symbols() = 0;
    virtual Core::Result<Core::SymbolVector> get_symbols_by_market(Core::Market market) = 0;
    virtual Core::Result<Core::SymbolVector> get_symbols_by_type(Core::SecurityType type) = 0;
    
    // Validation and maintenance
    virtual Core::Result<std::vector<std::string>> validate_data() = 0;
    virtual Core::Result<void> cleanup_inactive_securities() = 0;
    virtual Core::Result<void> rebuild_indices() = 0;
};

// Security service implementation
class SecurityService : public ISecurityService {
public:
    explicit SecurityService(
        std::shared_ptr<Data::ISecurityRepository> security_repo,
        SecurityServiceConfig config = {});
    
    ~SecurityService();
    
    // Disable copy
    SecurityService(const SecurityService&) = delete;
    SecurityService& operator=(const SecurityService&) = delete;
    
    // Enable move
    SecurityService(SecurityService&&) noexcept;
    SecurityService& operator=(SecurityService&&) noexcept;
    
    // ISecurityService implementation
    Core::Result<void> start() override;
    Core::Result<void> stop() override;
    bool is_running() const noexcept override;
    
    Core::Result<Core::SecurityInfo> get_security(const Core::Symbol& symbol) override;
    Core::Result<Core::SecurityInfoVector> get_securities(const Core::SymbolVector& symbols) override;
    Core::Result<Core::SecurityInfoVector> search_securities(const SecuritySearchCriteria& criteria) override;
    
    Core::Result<void> add_security(const Core::SecurityInfo& security) override;
    Core::Result<void> update_security(const Core::SecurityInfo& security) override;
    Core::Result<void> remove_security(const Core::Symbol& symbol) override;
    
    Core::Result<Core::BlockInfo> get_block(const std::string& name) override;
    Core::Result<Core::BlockInfoVector> get_all_blocks() override;
    Core::Result<Core::SymbolVector> get_block_securities(const std::string& block_name) override;
    
    Core::Result<void> add_block(const Core::BlockInfo& block) override;
    Core::Result<void> update_block(const Core::BlockInfo& block) override;
    Core::Result<void> remove_block(const std::string& name) override;
    
    Core::Result<void> add_security_to_block(const Core::Symbol& symbol, const std::string& block_name) override;
    Core::Result<void> remove_security_from_block(const Core::Symbol& symbol, const std::string& block_name) override;
    
    Core::Result<void> bulk_import(const Core::SecurityInfoVector& securities) override;
    Core::Result<Core::SecurityInfoVector> bulk_export(const Core::SymbolVector& symbols) override;
    
    Core::Result<void> sync_from_source(const std::string& source_name) override;
    Core::Result<void> sync_all_sources() override;
    
    Core::Result<SecurityStatistics> get_statistics() override;
    Core::Result<std::size_t> get_security_count() override;
    Core::Result<Core::SymbolVector> get_active_symbols() override;
    Core::Result<Core::SymbolVector> get_symbols_by_market(Core::Market market) override;
    Core::Result<Core::SymbolVector> get_symbols_by_type(Core::SecurityType type) override;
    
    Core::Result<std::vector<std::string>> validate_data() override;
    Core::Result<void> cleanup_inactive_securities() override;
    Core::Result<void> rebuild_indices() override;
    
    // Extended functionality
    Core::Result<void> update_config(const SecurityServiceConfig& config);
    Core::Result<SecurityServiceConfig> get_config() const;
    
    // Cache management
    Core::Result<void> clear_cache();
    Core::Result<void> refresh_cache();
    Core::Result<void> preload_securities(const Core::SymbolVector& symbols);
    
    // Advanced search
    Core::Result<Core::SymbolVector> fuzzy_search(const std::string& query, std::size_t max_results = 50);
    Core::Result<Core::SecurityInfoVector> get_similar_securities(const Core::Symbol& symbol, std::size_t max_results = 10);

private:
    SecurityServiceConfig config_;
    std::atomic<bool> running_;
    
    std::shared_ptr<Data::ISecurityRepository> security_repo_;
    
    // Cache management
    std::unordered_map<Core::Symbol, Core::SecurityInfo> security_cache_;
    std::unordered_map<std::string, Core::BlockInfo> block_cache_;
    std::unordered_map<std::string, Core::SymbolVector> block_securities_cache_;
    mutable std::mutex cache_mutex_;
    
    // Indices for fast searching
    std::unordered_map<Core::SecurityType, std::unordered_set<Core::Symbol>> type_index_;
    std::unordered_map<Core::Market, std::unordered_set<Core::Symbol>> market_index_;
    std::unordered_map<std::string, std::unordered_set<Core::Symbol>> sector_index_;
    mutable std::mutex index_mutex_;
    
    // Background maintenance
    std::unique_ptr<std::thread> maintenance_thread_;
    std::atomic<bool> stop_maintenance_;
    
    // Internal methods
    void maintenance_loop();
    void update_indices();
    void cleanup_cache();
    void auto_sync();
    
    bool matches_criteria(const Core::SecurityInfo& security, const SecuritySearchCriteria& criteria) const;
    bool matches_pattern(const std::string& text, const std::string& pattern) const;
    
    void update_security_cache(const Core::SecurityInfo& security);
    void remove_from_cache(const Core::Symbol& symbol);
    void update_indices(const Core::SecurityInfo& security);
    void remove_from_indices(const Core::Symbol& symbol);
    
    // Validation helpers
    bool validate_security(const Core::SecurityInfo& security) const;
    bool validate_block(const Core::BlockInfo& block) const;
    std::vector<std::string> check_data_consistency() const;
    
    // Search helpers
    double calculate_similarity(const Core::SecurityInfo& a, const Core::SecurityInfo& b) const;
    std::vector<std::pair<Core::Symbol, double>> rank_search_results(
        const std::string& query, 
        const std::unordered_set<Core::Symbol>& candidates) const;
};

// Security service factory
class SecurityServiceFactory {
public:
    static std::unique_ptr<ISecurityService> create(
        std::shared_ptr<Data::ISecurityRepository> security_repo,
        const SecurityServiceConfig& config = {});
    
    static std::unique_ptr<ISecurityService> create_with_default_data(
        std::shared_ptr<Data::ISecurityRepository> security_repo,
        const SecurityServiceConfig& config = {});
};

} // namespace DataHub::Services
