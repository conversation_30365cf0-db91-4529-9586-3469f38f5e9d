// Complete Service Implementation Example
// This example shows how to properly implement and use all DataHub services

#include "services/DataHubManager.h"
#include "services/ServiceFactory.h"
#include "api/DataHubAPI.h"
#include <iostream>
#include <chrono>
#include <thread>

using namespace DataHub;

// Example: Complete service integration
void complete_service_integration_example() {
    std::cout << "=== Complete Service Integration Example ===" << std::endl;
    
    try {
        // 1. Create DataHub configuration
        Services::DataHubConfig config;
        config.data_directory = "./complete_example_data";
        config.database_type = "sqlite";
        config.database_path = "./complete_example_data/datahub.db";
        config.log_level = "info";
        
        // Configure quote service
        config.quote_config.update_interval_ms = 50;
        config.quote_config.max_cache_size = 5000;
        config.quote_config.enable_realtime_push = true;
        config.quote_config.enable_history_cache = true;
        
        // Configure history service
        config.history_config.enable_compression = true;
        config.history_config.max_memory_cache_mb = 128;
        config.history_config.batch_size = 500;
        
        // Configure security service
        config.security_config.enable_auto_update = true;
        config.security_config.max_cache_size = 20000;
        config.security_config.enable_strict_validation = true;
        
        std::cout << "1. Creating service suite..." << std::endl;
        
        // 2. Create complete service suite using factory
        auto service_suite = Services::ServiceFactory::create_service_suite(config);
        
        if (!Services::ServiceFactory::validate_service_suite(service_suite)) {
            std::cerr << "Failed to create valid service suite" << std::endl;
            return;
        }
        
        std::cout << "2. Starting all services..." << std::endl;
        
        // 3. Start all services
        auto start_result = Services::ServiceFactory::start_service_suite(service_suite);
        if (!start_result.is_success()) {
            std::cerr << "Failed to start service suite: " << start_result.error().message << std::endl;
            return;
        }
        
        std::cout << "3. All services started successfully!" << std::endl;
        
        // 4. Test Quote Service
        std::cout << "4. Testing Quote Service..." << std::endl;
        
        // Create sample quote data
        Core::QuoteData sample_quote;
        sample_quote.symbol = "EXAMPLE";
        sample_quote.timestamp = std::chrono::system_clock::now();
        sample_quote.bid_price = 100.25;
        sample_quote.ask_price = 100.30;
        sample_quote.bid_size = 500;
        sample_quote.ask_size = 300;
        sample_quote.last_price = 100.28;
        sample_quote.volume = 10000;
        
        auto push_result = service_suite.quote_service->push_quote(sample_quote);
        if (push_result.is_success()) {
            std::cout << "   Quote pushed successfully" << std::endl;
            
            // Retrieve the quote
            auto get_result = service_suite.quote_service->get_latest_quote("EXAMPLE");
            if (get_result.is_success()) {
                const auto& retrieved_quote = get_result.value();
                std::cout << "   Retrieved quote: " << retrieved_quote.symbol 
                          << " @ $" << retrieved_quote.last_price << std::endl;
            }
        }
        
        // 5. Test Security Service
        std::cout << "5. Testing Security Service..." << std::endl;
        
        // Add sample security
        Core::SecurityInfo sample_security;
        sample_security.symbol = "EXAMPLE";
        sample_security.name = "Example Corporation";
        sample_security.type = Core::SecurityType::Stock;
        sample_security.market = Core::Market::NYSE;
        sample_security.currency = Core::Currency::USD;
        sample_security.sector = "Technology";
        sample_security.industry = "Software";
        sample_security.is_active = true;
        
        auto add_security_result = service_suite.security_service->add_security(sample_security);
        if (add_security_result.is_success()) {
            std::cout << "   Security added successfully" << std::endl;
            
            // Search for securities
            Services::SecuritySearchCriteria criteria;
            criteria.sector = "Technology";
            criteria.limit = 10;
            
            auto search_result = service_suite.security_service->search_securities(criteria);
            if (search_result.is_success()) {
                std::cout << "   Found " << search_result.value().size() << " technology securities" << std::endl;
            }
        }
        
        // 6. Test History Service
        std::cout << "6. Testing History Service..." << std::endl;
        
        // Create sample bar data
        Core::BarDataVector sample_bars;
        auto now = std::chrono::system_clock::now();
        
        for (int i = 0; i < 5; ++i) {
            Core::BarData bar;
            bar.symbol = "EXAMPLE";
            bar.timestamp = now - std::chrono::minutes(5 - i);
            bar.bar_size = Core::BarSize::OneMinute;
            bar.bar_type = Core::BarType::Time;
            bar.open = 100.0 + i * 0.1;
            bar.high = bar.open + 0.5;
            bar.low = bar.open - 0.3;
            bar.close = bar.open + 0.2;
            bar.volume = 5000 + i * 500;
            
            sample_bars.push_back(bar);
        }
        
        auto save_bars_result = service_suite.history_service->save_bars(sample_bars);
        if (save_bars_result.is_success()) {
            std::cout << "   Bars saved successfully" << std::endl;
            
            // Retrieve bars
            auto start_time = now - std::chrono::minutes(10);
            auto end_time = now;
            
            auto get_bars_result = service_suite.history_service->get_bars(
                "EXAMPLE", Core::BarSize::OneMinute, Core::BarType::Time, start_time, end_time);
            
            if (get_bars_result.is_success()) {
                std::cout << "   Retrieved " << get_bars_result.value().size() << " bars" << std::endl;
            }
        }
        
        // 7. Test Indicator Service
        std::cout << "7. Testing Indicator Service..." << std::endl;
        
        if (sample_bars.size() >= 3) {
            auto sma_result = service_suite.indicator_service->calculate_sma(
                "EXAMPLE", 3, now - std::chrono::minutes(10), now);
            
            if (sma_result.is_success()) {
                const auto& sma_values = sma_result.value();
                std::cout << "   Calculated " << sma_values.size() << " SMA values" << std::endl;
                
                if (!sma_values.empty()) {
                    double sma = sma_values.back().values.at("sma");
                    std::cout << "   Latest SMA: " << sma << std::endl;
                }
            }
        }
        
        // 8. Test Event Bus
        std::cout << "8. Testing Event Bus..." << std::endl;
        
        // Subscribe to events
        auto subscription_result = service_suite.event_bus->subscribe(
            Services::EventType::DataUpdated,
            [](const Services::Event& event) {
                std::cout << "   Event received: " << event.source 
                          << " - " << event.data.at("message") << std::endl;
            });
        
        if (subscription_result.is_success()) {
            std::cout << "   Subscribed to events successfully" << std::endl;
            
            // Publish a test event
            Services::Event test_event;
            test_event.type = Services::EventType::DataUpdated;
            test_event.source = "ExampleTest";
            test_event.timestamp = std::chrono::system_clock::now();
            test_event.data["message"] = "Test event from complete example";
            
            auto publish_result = service_suite.event_bus->publish(test_event);
            if (publish_result.is_success()) {
                std::cout << "   Event published successfully" << std::endl;
            }
            
            // Give some time for event processing
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        
        // 9. Performance and Statistics
        std::cout << "9. Checking service statistics..." << std::endl;
        
        // Quote service statistics
        if (service_suite.quote_service->is_running()) {
            std::cout << "   Quote Service: Running" << std::endl;
        }
        
        // History service statistics
        if (service_suite.history_service->is_running()) {
            std::cout << "   History Service: Running" << std::endl;
        }
        
        // Security service statistics
        if (service_suite.security_service->is_running()) {
            std::cout << "   Security Service: Running" << std::endl;
        }
        
        // Indicator service statistics
        if (service_suite.indicator_service->is_running()) {
            auto cache_size_result = service_suite.indicator_service->get_cache_size();
            if (cache_size_result.is_success()) {
                std::cout << "   Indicator Service: Running (Cache size: " 
                          << cache_size_result.value() << ")" << std::endl;
            }
        }
        
        // Event bus statistics
        auto event_stats_result = service_suite.event_bus->get_statistics();
        if (event_stats_result.is_success()) {
            const auto& stats = event_stats_result.value();
            std::cout << "   Event Bus: " << stats.events_published 
                      << " events published, " << stats.active_subscribers 
                      << " active subscribers" << std::endl;
        }
        
        // 10. Demonstrate service coordination
        std::cout << "10. Demonstrating service coordination..." << std::endl;
        
        // Create a coordinated workflow: Add security -> Add quote -> Calculate indicators
        Core::SecurityInfo coord_security;
        coord_security.symbol = "COORD";
        coord_security.name = "Coordination Test Corp";
        coord_security.type = Core::SecurityType::Stock;
        coord_security.market = Core::Market::NASDAQ;
        coord_security.currency = Core::Currency::USD;
        coord_security.sector = "Technology";
        coord_security.is_active = true;
        
        // Step 1: Add security
        auto coord_add_result = service_suite.security_service->add_security(coord_security);
        if (coord_add_result.is_success()) {
            std::cout << "   Step 1: Security added" << std::endl;
            
            // Step 2: Add quote
            Core::QuoteData coord_quote;
            coord_quote.symbol = "COORD";
            coord_quote.timestamp = std::chrono::system_clock::now();
            coord_quote.bid_price = 50.25;
            coord_quote.ask_price = 50.30;
            coord_quote.last_price = 50.28;
            coord_quote.volume = 5000;
            
            auto coord_quote_result = service_suite.quote_service->push_quote(coord_quote);
            if (coord_quote_result.is_success()) {
                std::cout << "   Step 2: Quote added" << std::endl;
                
                // Step 3: Add historical data for indicators
                Core::BarDataVector coord_bars;
                for (int i = 0; i < 10; ++i) {
                    Core::BarData bar;
                    bar.symbol = "COORD";
                    bar.timestamp = now - std::chrono::minutes(10 - i);
                    bar.bar_size = Core::BarSize::OneMinute;
                    bar.bar_type = Core::BarType::Time;
                    bar.open = 50.0 + i * 0.05;
                    bar.high = bar.open + 0.2;
                    bar.low = bar.open - 0.1;
                    bar.close = bar.open + 0.03;
                    bar.volume = 1000 + i * 100;
                    
                    coord_bars.push_back(bar);
                }
                
                auto coord_bars_result = service_suite.history_service->save_bars(coord_bars);
                if (coord_bars_result.is_success()) {
                    std::cout << "   Step 3: Historical data added" << std::endl;
                    
                    // Step 4: Calculate indicators
                    auto coord_sma_result = service_suite.indicator_service->calculate_sma(
                        "COORD", 5, now - std::chrono::minutes(15), now);
                    
                    if (coord_sma_result.is_success()) {
                        std::cout << "   Step 4: Indicators calculated" << std::endl;
                        std::cout << "   Coordination workflow completed successfully!" << std::endl;
                    }
                }
            }
        }
        
        // 11. Cleanup
        std::cout << "11. Stopping all services..." << std::endl;
        
        auto stop_result = Services::ServiceFactory::stop_service_suite(service_suite);
        if (stop_result.is_success()) {
            std::cout << "   All services stopped successfully" << std::endl;
        }
        
        std::cout << "=== Complete Service Integration Example Finished ===" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Exception occurred: " << e.what() << std::endl;
    }
}

int main() {
    std::cout << "DataHub Modern - Complete Service Implementation Example" << std::endl;
    std::cout << "=======================================================" << std::endl;
    
    // Run the complete service integration example
    complete_service_integration_example();
    
    std::cout << "\nExample completed!" << std::endl;
    return 0;
}
