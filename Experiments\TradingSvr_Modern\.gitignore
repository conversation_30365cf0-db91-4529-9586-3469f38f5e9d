# Build directories
build/
cmake-build-*/
out/

# IDE files
.vs/
.vscode/
*.vcxproj*
*.sln
*.suo
*.user
.idea/

# Compiled binaries
*.exe
*.dll
*.so
*.dylib
*.a
*.lib

# Object files
*.o
*.obj

# Debug files
*.pdb
*.ilk

# Logs
logs/
*.log

# Data files
data/
*.db
*.sqlite

# Models
models/
*.model
*.pkl
*.pt
*.onnx

# Configuration overrides
config/local_*
config/*_local.*

# Temporary files
*.tmp
*.temp
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Python cache (if using Python tools)
__pycache__/
*.py[cod]
*$py.class

# Coverage reports
*.gcov
*.gcda
*.gcno
coverage/

# Profiling data
*.prof
gmon.out

# Package files
*.tar.gz
*.zip
*.7z

# Backup files
*.bak
*.backup

# Test results
test_results/
*.xml
*.junit

# Documentation build
docs/_build/
docs/html/
docs/latex/

# Local environment
.env
.env.local
