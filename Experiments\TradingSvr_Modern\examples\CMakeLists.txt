# Examples configuration for TradingSvr_Modern
cmake_minimum_required(VERSION 3.20)

# Include directories
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/../include
)

# Example source files
set(EXAMPLE_SOURCES
    main.cpp
)

# Create example executable
add_executable(trading_server_example ${EXAMPLE_SOURCES})

# Link libraries
target_link_libraries(trading_server_example
    PRIVATE
        TradingSvr_Modern
        Threads::Threads
        Boost::system
        Boost::thread
        Boost::filesystem
)

# Compiler options
if(MSVC)
    target_compile_options(trading_server_example PRIVATE
        /W4 /WX- /bigobj
        /permissive-
        /Zc:__cplusplus
    )
else()
    target_compile_options(trading_server_example PRIVATE
        -Wall -Wextra -Wpedantic
        -Wno-unused-parameter
    )
endif()

# Copy configuration files to build directory
file(COPY ${CMAKE_CURRENT_SOURCE_DIR}/../config
     DESTINATION ${CMAKE_CURRENT_BINARY_DIR})

# Install example
install(TARGETS trading_server_example
    RUNTIME DESTINATION bin
)

# Install configuration files
install(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/../config
    DESTINATION share/TradingSvr_Modern
)
