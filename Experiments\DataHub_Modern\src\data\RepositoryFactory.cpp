// RepositoryFactory Implementation - Factory for creating data repositories
#include "data/RepositoryFactory.h"
#include "data/SqliteRepository.h"
#include "data/LevelDBRepository.h"
#include "data/HDF5Repository.h"
#include <filesystem>
#include <stdexcept>

namespace DataHub::Data {

// RepositoryFactory Implementation
std::unique_ptr<IDataRepository> RepositoryFactory::create_repository(
    RepositoryType type,
    const RepositoryConfig& config) {

    switch (type) {
        case RepositoryType::SQLite:
            return create_sqlite_repository(config.connection_string);

        case RepositoryType::LevelDB:
            return create_leveldb_repository(config.connection_string);

        case RepositoryType::HDF5:
            return create_hdf5_repository(config.connection_string);

        case RepositoryType::Memory:
            return create_memory_repository();

        default:
            throw std::invalid_argument("Unsupported repository type");
    }
}

std::unique_ptr<IDataRepository> RepositoryFactory::create_sqlite_repository(
    const std::string& database_path) {

    try {
        // Ensure directory exists
        std::filesystem::path db_path(database_path);
        if (db_path.has_parent_path()) {
            std::filesystem::create_directories(db_path.parent_path());
        }

        auto repository = std::make_unique<SqliteRepository>(database_path);

        // Test connection
        auto connect_result = repository->connect();
        if (!connect_result.is_success()) {
            return nullptr;
        }

        return repository;

    } catch (const std::exception& e) {
        return nullptr;
    }
}

std::unique_ptr<IDataRepository> RepositoryFactory::create_leveldb_repository(
    const std::string& database_path) {

    try {
        // Ensure directory exists
        std::filesystem::create_directories(database_path);

        auto repository = std::make_unique<LevelDBRepository>(database_path);

        // Test connection
        auto connect_result = repository->connect();
        if (!connect_result.is_success()) {
            return nullptr;
        }

        return repository;

    } catch (const std::exception& e) {
        return nullptr;
    }
}

std::unique_ptr<IDataRepository> RepositoryFactory::create_hdf5_repository(
    const std::string& file_path) {

    try {
        // Ensure directory exists
        std::filesystem::path hdf5_path(file_path);
        if (hdf5_path.has_parent_path()) {
            std::filesystem::create_directories(hdf5_path.parent_path());
        }

        auto repository = std::make_unique<HDF5Repository>(file_path);

        // Test connection
        auto connect_result = repository->connect();
        if (!connect_result.is_success()) {
            return nullptr;
        }

        return repository;

    } catch (const std::exception& e) {
        return nullptr;
    }
}

std::unique_ptr<IDataRepository> RepositoryFactory::create_memory_repository() {
    try {
        // Memory repository implementation would go here
        // For now, return SQLite with in-memory database
        return create_sqlite_repository(":memory:");

    } catch (const std::exception& e) {
        return nullptr;
    }
}

// Repository configuration helpers
RepositoryConfig RepositoryFactory::create_sqlite_config(
    const std::string& database_path,
    bool enable_wal,
    std::size_t cache_size_mb) {

    RepositoryConfig config;
    config.type = RepositoryType::SQLite;
    config.connection_string = database_path;
    config.enable_connection_pool = true;
    config.max_connections = 10;
    config.connection_timeout_ms = 5000;

    // SQLite specific options
    config.options["enable_wal"] = enable_wal ? "true" : "false";
    config.options["cache_size"] = std::to_string(cache_size_mb * 1024); // Convert to KB
    config.options["synchronous"] = "NORMAL";
    config.options["journal_mode"] = enable_wal ? "WAL" : "DELETE";

    return config;
}

RepositoryConfig RepositoryFactory::create_leveldb_config(
    const std::string& database_path,
    std::size_t cache_size_mb,
    bool enable_compression) {

    RepositoryConfig config;
    config.type = RepositoryType::LevelDB;
    config.connection_string = database_path;
    config.enable_connection_pool = false; // LevelDB doesn't support multiple connections
    config.max_connections = 1;

    // LevelDB specific options
    config.options["cache_size"] = std::to_string(cache_size_mb * 1024 * 1024); // Convert to bytes
    config.options["compression"] = enable_compression ? "snappy" : "none";
    config.options["write_buffer_size"] = "64MB";
    config.options["max_file_size"] = "64MB";

    return config;
}

RepositoryConfig RepositoryFactory::create_hdf5_config(
    const std::string& file_path,
    std::size_t chunk_size,
    int compression_level) {

    RepositoryConfig config;
    config.type = RepositoryType::HDF5;
    config.connection_string = file_path;
    config.enable_connection_pool = false; // HDF5 file-based access
    config.max_connections = 1;

    // HDF5 specific options
    config.options["chunk_size"] = std::to_string(chunk_size);
    config.options["compression"] = "gzip";
    config.options["compression_level"] = std::to_string(compression_level);
    config.options["shuffle"] = "true"; // Enable shuffle filter for better compression

    return config;
}

// Repository type detection
RepositoryType RepositoryFactory::detect_repository_type(const std::string& connection_string) {
    if (connection_string == ":memory:" || connection_string.ends_with(".db") || connection_string.ends_with(".sqlite")) {
        return RepositoryType::SQLite;
    }

    if (connection_string.ends_with(".h5") || connection_string.ends_with(".hdf5")) {
        return RepositoryType::HDF5;
    }

    // Check if it's a directory (likely LevelDB)
    if (std::filesystem::is_directory(connection_string) ||
        (!std::filesystem::exists(connection_string) && connection_string.find('.') == std::string::npos)) {
        return RepositoryType::LevelDB;
    }

    // Default to SQLite
    return RepositoryType::SQLite;
}

// Repository validation
bool RepositoryFactory::validate_config(const RepositoryConfig& config) {
    if (config.connection_string.empty()) {
        return false;
    }

    if (config.max_connections == 0) {
        return false;
    }

    switch (config.type) {
        case RepositoryType::SQLite:
            return validate_sqlite_config(config);

        case RepositoryType::LevelDB:
            return validate_leveldb_config(config);

        case RepositoryType::HDF5:
            return validate_hdf5_config(config);

        case RepositoryType::Memory:
            return true; // Memory repository is always valid

        default:
            return false;
    }
}

bool RepositoryFactory::validate_sqlite_config(const RepositoryConfig& config) {
    // Check if path is writable (except for :memory:)
    if (config.connection_string != ":memory:") {
        std::filesystem::path db_path(config.connection_string);
        if (db_path.has_parent_path()) {
            auto parent_path = db_path.parent_path();
            if (!std::filesystem::exists(parent_path)) {
                try {
                    std::filesystem::create_directories(parent_path);
                } catch (const std::exception& e) {
                    return false;
                }
            }
        }
    }

    return true;
}

bool RepositoryFactory::validate_leveldb_config(const RepositoryConfig& config) {
    // Check if directory is accessible
    std::filesystem::path db_path(config.connection_string);

    if (!std::filesystem::exists(db_path)) {
        try {
            std::filesystem::create_directories(db_path);
        } catch (const std::exception& e) {
            return false;
        }
    }

    return std::filesystem::is_directory(db_path);
}

bool RepositoryFactory::validate_hdf5_config(const RepositoryConfig& config) {
    // Check if file path is valid
    std::filesystem::path file_path(config.connection_string);

    if (file_path.has_parent_path()) {
        auto parent_path = file_path.parent_path();
        if (!std::filesystem::exists(parent_path)) {
            try {
                std::filesystem::create_directories(parent_path);
            } catch (const std::exception& e) {
                return false;
            }
        }
    }

    return true;
}

// Repository testing
Core::Result<void> RepositoryFactory::test_repository(IDataRepository* repository) {
    if (!repository) {
        return Core::make_error<void>(Core::ErrorCode::InvalidArgument, "Repository is null");
    }

    // Test connection
    if (!repository->is_connected()) {
        auto connect_result = repository->connect();
        if (!connect_result.is_success()) {
            return connect_result;
        }
    }

    // Test basic operations
    try {
        // Test transaction support
        auto begin_result = repository->begin_transaction();
        if (begin_result.is_success()) {
            auto rollback_result = repository->rollback_transaction();
            if (!rollback_result.is_success()) {
                return rollback_result;
            }
        }

        return Core::make_success();

    } catch (const std::exception& e) {
        return Core::make_error<void>(Core::ErrorCode::DatabaseError,
                                     "Repository test failed: " + std::string(e.what()));
    }
}

// Repository migration
Core::Result<void> RepositoryFactory::migrate_repository(
    IDataRepository* source,
    IDataRepository* destination,
    const MigrationOptions& options) {

    if (!source || !destination) {
        return Core::make_error<void>(Core::ErrorCode::InvalidArgument, "Source or destination repository is null");
    }

    try {
        // Ensure both repositories are connected
        if (!source->is_connected()) {
            auto connect_result = source->connect();
            if (!connect_result.is_success()) {
                return connect_result;
            }
        }

        if (!destination->is_connected()) {
            auto connect_result = destination->connect();
            if (!connect_result.is_success()) {
                return connect_result;
            }
        }

        // Begin transaction on destination
        auto begin_result = destination->begin_transaction();
        if (!begin_result.is_success()) {
            return begin_result;
        }

        // Migration logic would go here
        // This is a placeholder for the actual migration implementation

        // Commit transaction
        auto commit_result = destination->commit_transaction();
        if (!commit_result.is_success()) {
            destination->rollback_transaction();
            return commit_result;
        }

        return Core::make_success();

    } catch (const std::exception& e) {
        destination->rollback_transaction();
        return Core::make_error<void>(Core::ErrorCode::DatabaseError,
                                     "Migration failed: " + std::string(e.what()));
    }
}

} // namespace DataHub::Data
