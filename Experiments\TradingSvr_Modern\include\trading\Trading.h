/**
 * @file Trading.h
 * @brief Main header for modern trading system
 * <AUTHOR> Team
 * @date 2024
 */

#pragma once

// Core types and utilities
#include "Types.h"
#include "Events.h"

// Core trading components
#include "Position.h"
#include "Portfolio.h"
#include "OrderManager.h"
#include "Strategy.h"

// Strategy implementations
#include "strategies/FuturesStrategy.h"
#include "strategies/StockStrategy.h"

// Data and model components
#include "DataProvider.h"
#include "ModelManager.h"
#include "ExpressionEngine.h"

// Network and communication
#include "NetworkManager.h"

// Main server
#include "TradingServer.h"

/**
 * @brief Main namespace for the modern trading system
 */
namespace RoboQuant::Trading {

/**
 * @brief Version information
 */
constexpr const char* VERSION = "1.0.0";
constexpr int VERSION_MAJOR = 1;
constexpr int VERSION_MINOR = 0;
constexpr int VERSION_PATCH = 0;

/**
 * @brief Initialize the trading system
 * 
 * This function should be called once at the beginning of the application
 * to initialize global resources and register built-in components.
 */
void initialize();

/**
 * @brief Cleanup the trading system
 * 
 * This function should be called at the end of the application
 * to cleanup global resources.
 */
void cleanup();

/**
 * @brief Get version string
 */
[[nodiscard]] std::string get_version();

/**
 * @brief Get build information
 */
[[nodiscard]] std::unordered_map<std::string, std::string> get_build_info();

/**
 * @brief Factory functions for common components
 */
namespace Factory {

/**
 * @brief Create a default trading server with common configuration
 */
[[nodiscard]] std::unique_ptr<TradingServer> create_default_server(uint16_t port = 8080);

/**
 * @brief Create a simulation trading server for backtesting
 */
[[nodiscard]] std::unique_ptr<TradingServer> create_simulation_server(
    Amount initial_capital = 1000000.0,
    const std::string& data_source = "csv"
);

/**
 * @brief Create a live trading server
 */
[[nodiscard]] std::unique_ptr<TradingServer> create_live_server(
    const std::string& broker_config,
    const std::string& data_config
);

/**
 * @brief Create a portfolio with default settings
 */
[[nodiscard]] std::unique_ptr<Portfolio> create_default_portfolio(
    const std::string& name,
    Amount initial_capital
);

/**
 * @brief Create a futures strategy with default configuration
 */
[[nodiscard]] std::unique_ptr<Strategy> create_futures_strategy(
    const std::string& strategy_id,
    const std::unordered_map<std::string, std::variant<int, double, std::string, bool>>& parameters = {}
);

/**
 * @brief Create a stock strategy with default configuration
 */
[[nodiscard]] std::unique_ptr<Strategy> create_stock_strategy(
    const std::string& strategy_id,
    const std::unordered_map<std::string, std::variant<int, double, std::string, bool>>& parameters = {}
);

/**
 * @brief Create a simulation order executor
 */
[[nodiscard]] std::unique_ptr<OrderExecutor> create_simulation_executor(
    std::function<Price(const AssetId&)> price_provider
);

/**
 * @brief Create an expression manager with built-in functions
 */
[[nodiscard]] std::unique_ptr<ExpressionManager> create_expression_manager();

} // namespace Factory

/**
 * @brief Utility functions
 */
namespace Utils {

/**
 * @brief Convert time point to string
 */
[[nodiscard]] std::string time_to_string(TimePoint time_point, const std::string& format = "%Y-%m-%d %H:%M:%S");

/**
 * @brief Parse string to time point
 */
[[nodiscard]] std::optional<TimePoint> string_to_time(const std::string& time_string, const std::string& format = "%Y-%m-%d %H:%M:%S");

/**
 * @brief Format amount as currency string
 */
[[nodiscard]] std::string format_currency(Amount amount, const std::string& currency = "USD", int precision = 2);

/**
 * @brief Format percentage
 */
[[nodiscard]] std::string format_percentage(double value, int precision = 2);

/**
 * @brief Calculate compound annual growth rate (CAGR)
 */
[[nodiscard]] double calculate_cagr(Amount initial_value, Amount final_value, Duration period);

/**
 * @brief Calculate Sharpe ratio
 */
[[nodiscard]] double calculate_sharpe_ratio(const std::vector<double>& returns, double risk_free_rate = 0.0);

/**
 * @brief Calculate maximum drawdown
 */
[[nodiscard]] double calculate_max_drawdown(const std::vector<double>& values);

/**
 * @brief Calculate volatility (annualized)
 */
[[nodiscard]] double calculate_volatility(const std::vector<double>& returns, Duration period = std::chrono::days(252));

/**
 * @brief Generate unique ID
 */
[[nodiscard]] std::string generate_unique_id(const std::string& prefix = "");

/**
 * @brief Validate asset ID format
 */
[[nodiscard]] bool is_valid_asset_id(const AssetId& asset_id);

/**
 * @brief Parse asset ID components
 */
struct AssetIdComponents {
    std::string symbol;
    std::string exchange;
    std::optional<std::string> asset_class;
    std::optional<std::string> expiry;
};

[[nodiscard]] std::optional<AssetIdComponents> parse_asset_id(const AssetId& asset_id);

/**
 * @brief JSON serialization helpers
 */
[[nodiscard]] std::string to_json(const QuoteData& quote);
[[nodiscard]] std::string to_json(const BarData& bar);
[[nodiscard]] std::string to_json(const OrderRequest& request);
[[nodiscard]] std::string to_json(const OrderFill& fill);
[[nodiscard]] std::string to_json(const PerformanceMetrics& metrics);

[[nodiscard]] std::optional<QuoteData> quote_from_json(const std::string& json);
[[nodiscard]] std::optional<BarData> bar_from_json(const std::string& json);
[[nodiscard]] std::optional<OrderRequest> order_request_from_json(const std::string& json);
[[nodiscard]] std::optional<OrderFill> order_fill_from_json(const std::string& json);
[[nodiscard]] std::optional<PerformanceMetrics> performance_metrics_from_json(const std::string& json);

} // namespace Utils

/**
 * @brief Configuration helpers
 */
namespace Config {

/**
 * @brief Load trading server configuration from file
 */
[[nodiscard]] std::optional<TradingServerConfig> load_server_config(const std::string& config_file);

/**
 * @brief Save trading server configuration to file
 */
[[nodiscard]] bool save_server_config(const TradingServerConfig& config, const std::string& config_file);

/**
 * @brief Load strategy configurations from file
 */
[[nodiscard]] std::vector<StrategyConfig> load_strategy_configs(const std::string& config_file);

/**
 * @brief Save strategy configurations to file
 */
[[nodiscard]] bool save_strategy_configs(const std::vector<StrategyConfig>& configs, const std::string& config_file);

/**
 * @brief Load portfolio configurations from file
 */
[[nodiscard]] std::vector<PortfolioConfig> load_portfolio_configs(const std::string& config_file);

/**
 * @brief Save portfolio configurations to file
 */
[[nodiscard]] bool save_portfolio_configs(const std::vector<PortfolioConfig>& configs, const std::string& config_file);

/**
 * @brief Load model configurations from file
 */
[[nodiscard]] std::vector<ModelConfig> load_model_configs(const std::string& config_file);

/**
 * @brief Save model configurations to file
 */
[[nodiscard]] bool save_model_configs(const std::vector<ModelConfig>& configs, const std::string& config_file);

/**
 * @brief Create default configurations
 */
[[nodiscard]] TradingServerConfig create_default_server_config();
[[nodiscard]] StrategyConfig create_default_strategy_config(const std::string& strategy_type, const std::string& strategy_id);
[[nodiscard]] PortfolioConfig create_default_portfolio_config(const std::string& portfolio_id, Amount initial_capital);
[[nodiscard]] RiskLimits create_default_risk_limits();

} // namespace Config

} // namespace RoboQuant::Trading
