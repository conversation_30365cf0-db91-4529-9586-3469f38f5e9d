﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <PropertyGroup>
    <ResolveNugetPackages>false</ResolveNugetPackages>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{E845B234-6F99-32FB-9478-5DF9566AB4BB}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>ZERO_CHECK</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\DataHub_Modern\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\DataHub_Modern\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <Midl>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\DataHub_Modern\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <Midl>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\DataHub_Modern\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\CMakeFiles\22537e2d600951d784a1c15ac337943a\generate.stamp.rule">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"D:\Program Files\CMake\bin\cmake.exe" -SE:/lab/RoboQuant/src/Experiments/DataHub_Modern -BE:/lab/RoboQuant/src/Experiments/DataHub_Modern/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/DataHub_Modern.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\Program Files\CMake\share\cmake-3.25\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeCXXInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeCommonLanguageInclude.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeDependentOption.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeGenericSystem.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeInitializeConfigs.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeLanguageInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakePackageConfigHelpers.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeParseArguments.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeRCInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeSystemSpecificInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeSystemSpecificInitialize.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CPack.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CPackComponent.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CTest.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CTestTargets.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CTestUseLaunchers.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CheckCXXCompilerFlag.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CheckCXXSourceCompiles.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CheckIncludeFileCXX.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CheckLibraryExists.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\MSVC-CXX.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\MSVC.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\DartConfiguration.tcl.in;D:\Program Files\CMake\share\cmake-3.25\Modules\ExternalProject.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FetchContent.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FetchContent\CMakeLists.cmake.in;D:\Program Files\CMake\share\cmake-3.25\Modules\FindDoxygen.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FindGit.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FindPackageHandleStandardArgs.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FindPackageMessage.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FindThreads.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\GNUInstallDirs.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Internal\CheckCompilerFlag.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Internal\CheckFlagCommonConfig.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Internal\CheckSourceCompiles.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows-MSVC-CXX.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows-MSVC.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\WindowsPaths.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\WriteBasicConfigVersionFile.cmake;D:\Program Files\CMake\share\cmake-3.25\Templates\CPackConfig.cmake.in;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\CMakeLists.txt;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\CMakeFiles\3.25.0-rc2\CMakeCXXCompiler.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\CMakeFiles\3.25.0-rc2\CMakeRCCompiler.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\CMakeFiles\3.25.0-rc2\CMakeSystem.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\CMake\CatchConfigOptions.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\CMake\CatchMiscFunctions.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\CMakeLists.txt;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\extras\Catch.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\CMakeLists.txt;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\catch_user_config.hpp.in;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\CMakeLists.txt;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\support\cmake\JoinPaths.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\support\cmake\fmt-config.cmake.in;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\support\cmake\fmt.pc.in;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\nlohmann_json-src\CMakeLists.txt;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\nlohmann_json-src\cmake\config.cmake.in;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\nlohmann_json-src\cmake\nlohmann_jsonConfigVersion.cmake.in;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\nlohmann_json-src\cmake\pkg-config.pc.in;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\spdlog-src\CMakeLists.txt;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\spdlog-src\cmake\ide.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\spdlog-src\cmake\utils.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\examples\CMakeLists.txt;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\tests\CMakeLists.txt;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\CMakeFiles\generate.stamp;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\spdlog-build\CMakeFiles\generate.stamp;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\nlohmann_json-build\CMakeFiles\generate.stamp;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-build\CMakeFiles\generate.stamp;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-build\CMakeFiles\generate.stamp;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-build\src\CMakeFiles\generate.stamp;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\tests\CMakeFiles\generate.stamp;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\examples\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"D:\Program Files\CMake\bin\cmake.exe" -SE:/lab/RoboQuant/src/Experiments/DataHub_Modern -BE:/lab/RoboQuant/src/Experiments/DataHub_Modern/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/DataHub_Modern.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\Program Files\CMake\share\cmake-3.25\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeCXXInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeCommonLanguageInclude.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeDependentOption.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeGenericSystem.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeInitializeConfigs.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeLanguageInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakePackageConfigHelpers.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeParseArguments.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeRCInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeSystemSpecificInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeSystemSpecificInitialize.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CPack.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CPackComponent.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CTest.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CTestTargets.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CTestUseLaunchers.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CheckCXXCompilerFlag.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CheckCXXSourceCompiles.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CheckIncludeFileCXX.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CheckLibraryExists.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\MSVC-CXX.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\MSVC.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\DartConfiguration.tcl.in;D:\Program Files\CMake\share\cmake-3.25\Modules\ExternalProject.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FetchContent.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FetchContent\CMakeLists.cmake.in;D:\Program Files\CMake\share\cmake-3.25\Modules\FindDoxygen.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FindGit.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FindPackageHandleStandardArgs.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FindPackageMessage.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FindThreads.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\GNUInstallDirs.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Internal\CheckCompilerFlag.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Internal\CheckFlagCommonConfig.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Internal\CheckSourceCompiles.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows-MSVC-CXX.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows-MSVC.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\WindowsPaths.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\WriteBasicConfigVersionFile.cmake;D:\Program Files\CMake\share\cmake-3.25\Templates\CPackConfig.cmake.in;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\CMakeLists.txt;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\CMakeFiles\3.25.0-rc2\CMakeCXXCompiler.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\CMakeFiles\3.25.0-rc2\CMakeRCCompiler.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\CMakeFiles\3.25.0-rc2\CMakeSystem.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\CMake\CatchConfigOptions.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\CMake\CatchMiscFunctions.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\CMakeLists.txt;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\extras\Catch.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\CMakeLists.txt;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\catch_user_config.hpp.in;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\CMakeLists.txt;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\support\cmake\JoinPaths.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\support\cmake\fmt-config.cmake.in;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\support\cmake\fmt.pc.in;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\nlohmann_json-src\CMakeLists.txt;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\nlohmann_json-src\cmake\config.cmake.in;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\nlohmann_json-src\cmake\nlohmann_jsonConfigVersion.cmake.in;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\nlohmann_json-src\cmake\pkg-config.pc.in;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\spdlog-src\CMakeLists.txt;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\spdlog-src\cmake\ide.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\spdlog-src\cmake\utils.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\examples\CMakeLists.txt;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\tests\CMakeLists.txt;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\CMakeFiles\generate.stamp;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\spdlog-build\CMakeFiles\generate.stamp;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\nlohmann_json-build\CMakeFiles\generate.stamp;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-build\CMakeFiles\generate.stamp;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-build\CMakeFiles\generate.stamp;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-build\src\CMakeFiles\generate.stamp;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\tests\CMakeFiles\generate.stamp;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\examples\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"D:\Program Files\CMake\bin\cmake.exe" -SE:/lab/RoboQuant/src/Experiments/DataHub_Modern -BE:/lab/RoboQuant/src/Experiments/DataHub_Modern/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/DataHub_Modern.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\Program Files\CMake\share\cmake-3.25\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeCXXInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeCommonLanguageInclude.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeDependentOption.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeGenericSystem.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeInitializeConfigs.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeLanguageInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakePackageConfigHelpers.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeParseArguments.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeRCInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeSystemSpecificInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeSystemSpecificInitialize.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CPack.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CPackComponent.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CTest.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CTestTargets.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CTestUseLaunchers.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CheckCXXCompilerFlag.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CheckCXXSourceCompiles.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CheckIncludeFileCXX.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CheckLibraryExists.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\MSVC-CXX.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\MSVC.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\DartConfiguration.tcl.in;D:\Program Files\CMake\share\cmake-3.25\Modules\ExternalProject.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FetchContent.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FetchContent\CMakeLists.cmake.in;D:\Program Files\CMake\share\cmake-3.25\Modules\FindDoxygen.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FindGit.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FindPackageHandleStandardArgs.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FindPackageMessage.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FindThreads.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\GNUInstallDirs.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Internal\CheckCompilerFlag.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Internal\CheckFlagCommonConfig.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Internal\CheckSourceCompiles.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows-MSVC-CXX.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows-MSVC.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\WindowsPaths.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\WriteBasicConfigVersionFile.cmake;D:\Program Files\CMake\share\cmake-3.25\Templates\CPackConfig.cmake.in;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\CMakeLists.txt;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\CMakeFiles\3.25.0-rc2\CMakeCXXCompiler.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\CMakeFiles\3.25.0-rc2\CMakeRCCompiler.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\CMakeFiles\3.25.0-rc2\CMakeSystem.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\CMake\CatchConfigOptions.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\CMake\CatchMiscFunctions.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\CMakeLists.txt;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\extras\Catch.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\CMakeLists.txt;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\catch_user_config.hpp.in;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\CMakeLists.txt;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\support\cmake\JoinPaths.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\support\cmake\fmt-config.cmake.in;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\support\cmake\fmt.pc.in;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\nlohmann_json-src\CMakeLists.txt;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\nlohmann_json-src\cmake\config.cmake.in;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\nlohmann_json-src\cmake\nlohmann_jsonConfigVersion.cmake.in;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\nlohmann_json-src\cmake\pkg-config.pc.in;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\spdlog-src\CMakeLists.txt;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\spdlog-src\cmake\ide.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\spdlog-src\cmake\utils.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\examples\CMakeLists.txt;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\tests\CMakeLists.txt;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\CMakeFiles\generate.stamp;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\spdlog-build\CMakeFiles\generate.stamp;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\nlohmann_json-build\CMakeFiles\generate.stamp;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-build\CMakeFiles\generate.stamp;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-build\CMakeFiles\generate.stamp;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-build\src\CMakeFiles\generate.stamp;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\tests\CMakeFiles\generate.stamp;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\examples\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"D:\Program Files\CMake\bin\cmake.exe" -SE:/lab/RoboQuant/src/Experiments/DataHub_Modern -BE:/lab/RoboQuant/src/Experiments/DataHub_Modern/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/DataHub_Modern.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\Program Files\CMake\share\cmake-3.25\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeCXXInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeCommonLanguageInclude.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeDependentOption.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeGenericSystem.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeInitializeConfigs.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeLanguageInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakePackageConfigHelpers.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeParseArguments.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeRCInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeSystemSpecificInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeSystemSpecificInitialize.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CPack.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CPackComponent.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CTest.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CTestTargets.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CTestUseLaunchers.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CheckCXXCompilerFlag.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CheckCXXSourceCompiles.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CheckIncludeFileCXX.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CheckLibraryExists.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\MSVC-CXX.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\MSVC.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\DartConfiguration.tcl.in;D:\Program Files\CMake\share\cmake-3.25\Modules\ExternalProject.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FetchContent.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FetchContent\CMakeLists.cmake.in;D:\Program Files\CMake\share\cmake-3.25\Modules\FindDoxygen.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FindGit.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FindPackageHandleStandardArgs.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FindPackageMessage.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FindThreads.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\GNUInstallDirs.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Internal\CheckCompilerFlag.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Internal\CheckFlagCommonConfig.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Internal\CheckSourceCompiles.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows-MSVC-CXX.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows-MSVC.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\WindowsPaths.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\WriteBasicConfigVersionFile.cmake;D:\Program Files\CMake\share\cmake-3.25\Templates\CPackConfig.cmake.in;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\CMakeLists.txt;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\CMakeFiles\3.25.0-rc2\CMakeCXXCompiler.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\CMakeFiles\3.25.0-rc2\CMakeRCCompiler.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\CMakeFiles\3.25.0-rc2\CMakeSystem.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\CMake\CatchConfigOptions.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\CMake\CatchMiscFunctions.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\CMakeLists.txt;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\extras\Catch.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\CMakeLists.txt;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\catch_user_config.hpp.in;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\CMakeLists.txt;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\support\cmake\JoinPaths.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\support\cmake\fmt-config.cmake.in;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\support\cmake\fmt.pc.in;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\nlohmann_json-src\CMakeLists.txt;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\nlohmann_json-src\cmake\config.cmake.in;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\nlohmann_json-src\cmake\nlohmann_jsonConfigVersion.cmake.in;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\nlohmann_json-src\cmake\pkg-config.pc.in;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\spdlog-src\CMakeLists.txt;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\spdlog-src\cmake\ide.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\spdlog-src\cmake\utils.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\examples\CMakeLists.txt;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\tests\CMakeLists.txt;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\CMakeFiles\generate.stamp;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\spdlog-build\CMakeFiles\generate.stamp;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\nlohmann_json-build\CMakeFiles\generate.stamp;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-build\CMakeFiles\generate.stamp;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-build\CMakeFiles\generate.stamp;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-build\src\CMakeFiles\generate.stamp;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\tests\CMakeFiles\generate.stamp;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\examples\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>