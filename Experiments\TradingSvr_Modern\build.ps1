# Build script for TradingSvr_Modern on Windows
param(
    [string]$BuildType = "Release",
    [switch]$Clean,
    [switch]$Test,
    [switch]$Install,
    [string]$Generator = "Visual Studio 17 2022"
)

# Colors for output
$Red = [System.ConsoleColor]::Red
$Green = [System.ConsoleColor]::Green
$Yellow = [System.ConsoleColor]::Yellow
$Blue = [System.ConsoleColor]::Blue

function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Info($message) {
    Write-ColorOutput $Blue "INFO: $message"
}

function Write-Success($message) {
    Write-ColorOutput $Green "SUCCESS: $message"
}

function Write-Warning($message) {
    Write-ColorOutput $Yellow "WARNING: $message"
}

function Write-Error($message) {
    Write-ColorOutput $Red "ERROR: $message"
}

# Check if CMake is available
if (-not (Get-Command cmake -ErrorAction SilentlyContinue)) {
    Write-Error "CMake is not installed or not in PATH"
    exit 1
}

# Project root directory
$ProjectRoot = $PSScriptRoot
$BuildDir = Join-Path $ProjectRoot "build"

Write-Info "Building TradingSvr_Modern"
Write-Info "Build Type: $BuildType"
Write-Info "Generator: $Generator"
Write-Info "Project Root: $ProjectRoot"
Write-Info "Build Directory: $BuildDir"

# Clean build directory if requested
if ($Clean -and (Test-Path $BuildDir)) {
    Write-Info "Cleaning build directory..."
    Remove-Item -Recurse -Force $BuildDir
    Write-Success "Build directory cleaned"
}

# Create build directory
if (-not (Test-Path $BuildDir)) {
    Write-Info "Creating build directory..."
    New-Item -ItemType Directory -Path $BuildDir | Out-Null
}

# Change to build directory
Push-Location $BuildDir

try {
    # Configure
    Write-Info "Configuring project..."
    $ConfigureArgs = @(
        ".."
        "-G", $Generator
        "-DCMAKE_BUILD_TYPE=$BuildType"
        "-DCMAKE_INSTALL_PREFIX=install"
    )
    
    & cmake @ConfigureArgs
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Configuration failed"
        exit 1
    }
    Write-Success "Configuration completed"
    
    # Build
    Write-Info "Building project..."
    $BuildArgs = @(
        "--build", "."
        "--config", $BuildType
        "--parallel"
    )
    
    & cmake @BuildArgs
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Build failed"
        exit 1
    }
    Write-Success "Build completed"
    
    # Run tests if requested
    if ($Test) {
        Write-Info "Running tests..."
        & ctest --output-on-failure --config $BuildType
        if ($LASTEXITCODE -ne 0) {
            Write-Warning "Some tests failed"
        } else {
            Write-Success "All tests passed"
        }
    }
    
    # Install if requested
    if ($Install) {
        Write-Info "Installing..."
        $InstallArgs = @(
            "--install", "."
            "--config", $BuildType
        )
        
        & cmake @InstallArgs
        if ($LASTEXITCODE -ne 0) {
            Write-Error "Installation failed"
            exit 1
        }
        Write-Success "Installation completed"
    }
    
    Write-Success "Build process completed successfully!"
    Write-Info "Executable location: $BuildDir\examples\$BuildType\trading_server_example.exe"
    
} finally {
    # Return to original directory
    Pop-Location
}

# Display usage information
Write-Info ""
Write-Info "Usage examples:"
Write-Info "  .\build.ps1                          # Release build"
Write-Info "  .\build.ps1 -BuildType Debug         # Debug build"
Write-Info "  .\build.ps1 -Clean                   # Clean and build"
Write-Info "  .\build.ps1 -Test                    # Build and run tests"
Write-Info "  .\build.ps1 -Install                 # Build and install"
Write-Info "  .\build.ps1 -Clean -Test -Install    # Full build cycle"
