﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\src\services\QuoteService.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\src\services\HistoryService.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\src\services\SecurityService.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\src\services\IndicatorService.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\src\services\EventBus.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\src\services\ServiceFactory.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\include\services\IDataService.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\include\services\QuoteService.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\include\services\HistoryService.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\include\services\SecurityService.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\include\services\IndicatorService.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\include\services\EventBus.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\include\services\ServiceFactory.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Natvis Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\nlohmann_json-src\nlohmann_json.natvis" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{B51AFE03-D7FA-3F7D-BDDF-881A2EF2E39B}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{ED3CB8E5-FD7A-3664-A7D3-A35456B4DB16}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
