// DataHub Modern - Performance Benchmark Tests
// This file contains performance benchmarks for the DataHub services

#include <catch2/catch_test_macros.hpp>
#include <catch2/benchmark/catch_benchmark.hpp>
#include "services/QuoteService.h"
#include "services/HistoryService_Enhanced.h"
#include "services/SecurityService_Enhanced.h"
#include "data/SqliteRepository.h"
#include <chrono>
#include <random>
#include <vector>
#include <string>

using namespace DataHub;

// Utility class for generating test data
class TestDataGenerator {
public:
    TestDataGenerator() : gen_(std::random_device{}()), price_dist_(50.0, 200.0), volume_dist_(100, 10000) {}
    
    Core::QuoteData generate_quote(const std::string& symbol) {
        Core::QuoteData quote;
        quote.symbol = symbol;
        quote.timestamp = std::chrono::system_clock::now();
        
        double base_price = price_dist_(gen_);
        quote.bid_price = base_price - 0.01;
        quote.ask_price = base_price + 0.01;
        quote.last_price = base_price;
        quote.bid_size = volume_dist_(gen_);
        quote.ask_size = volume_dist_(gen_);
        quote.volume = volume_dist_(gen_);
        
        return quote;
    }
    
    Core::BarData generate_bar(const std::string& symbol, const std::chrono::system_clock::time_point& timestamp) {
        Core::BarData bar;
        bar.symbol = symbol;
        bar.timestamp = timestamp;
        bar.bar_size = Core::BarSize::OneMinute;
        bar.bar_type = Core::BarType::Time;
        
        double base_price = price_dist_(gen_);
        bar.open = base_price;
        bar.high = base_price + 2.0;
        bar.low = base_price - 1.0;
        bar.close = base_price + 0.5;
        bar.volume = volume_dist_(gen_);
        
        return bar;
    }
    
    Core::SecurityInfo generate_security(const std::string& symbol) {
        Core::SecurityInfo security;
        security.symbol = symbol;
        security.name = symbol + " Corporation";
        security.type = Core::SecurityType::Stock;
        security.market = Core::Market::NYSE;
        security.currency = Core::Currency::USD;
        security.sector = "Technology";
        security.industry = "Software";
        security.is_active = true;
        
        return security;
    }
    
    std::vector<std::string> generate_symbols(size_t count) {
        std::vector<std::string> symbols;
        symbols.reserve(count);
        
        for (size_t i = 0; i < count; ++i) {
            symbols.push_back("SYM" + std::to_string(i));
        }
        
        return symbols;
    }

private:
    std::mt19937 gen_;
    std::uniform_real_distribution<double> price_dist_;
    std::uniform_int_distribution<int> volume_dist_;
};

// Performance test fixture
class PerformanceTestFixture {
public:
    PerformanceTestFixture() {
        // Create in-memory repositories for performance testing
        quote_repo_ = std::make_shared<Data::SqliteRepository>(":memory:");
        tick_repo_ = std::make_shared<Data::SqliteRepository>(":memory:");
        bar_repo_ = std::make_shared<Data::SqliteRepository>(":memory:");
        security_repo_ = std::make_shared<Data::SqliteRepository>(":memory:");
        
        // Create services with performance-optimized configurations
        Services::QuoteServiceConfig quote_config;
        quote_config.update_interval_ms = 1;
        quote_config.max_cache_size = 10000;
        quote_config.enable_realtime_push = true;
        
        Services::HistoryServiceConfig history_config;
        history_config.enable_compression = false;
        history_config.max_memory_cache_mb = 100;
        history_config.batch_size = 1000;
        
        Services::SecurityServiceConfig security_config;
        security_config.enable_auto_update = false;
        security_config.max_cache_size = 10000;
        
        quote_service_ = std::make_unique<Services::QuoteService>(
            quote_repo_, tick_repo_, quote_config);
        
        history_service_ = std::make_unique<Services::HistoryService>(
            bar_repo_, tick_repo_, history_config);
        
        security_service_ = std::make_unique<Services::SecurityService>(
            security_repo_, security_config);
        
        // Start services
        quote_service_->start();
        history_service_->start();
        security_service_->start();
    }
    
    ~PerformanceTestFixture() {
        quote_service_->stop();
        history_service_->stop();
        security_service_->stop();
    }

public:
    std::shared_ptr<Data::SqliteRepository> quote_repo_;
    std::shared_ptr<Data::SqliteRepository> tick_repo_;
    std::shared_ptr<Data::SqliteRepository> bar_repo_;
    std::shared_ptr<Data::SqliteRepository> security_repo_;
    
    std::unique_ptr<Services::QuoteService> quote_service_;
    std::unique_ptr<Services::HistoryService> history_service_;
    std::unique_ptr<Services::SecurityService> security_service_;
    
    TestDataGenerator generator_;
};

TEST_CASE("Quote Service Performance", "[performance][quote]") {
    PerformanceTestFixture fixture;
    
    SECTION("Single quote operations") {
        auto test_quote = fixture.generator_.generate_quote("PERF_TEST");
        
        BENCHMARK("Push single quote") {
            return fixture.quote_service_->push_quote(test_quote);
        };
        
        // Push a quote first for retrieval benchmark
        fixture.quote_service_->push_quote(test_quote);
        
        BENCHMARK("Get single quote") {
            return fixture.quote_service_->get_latest_quote("PERF_TEST");
        };
    }
    
    SECTION("Batch quote operations") {
        const size_t batch_size = 1000;
        auto symbols = fixture.generator_.generate_symbols(batch_size);
        
        // Pre-populate with quotes
        for (const auto& symbol : symbols) {
            auto quote = fixture.generator_.generate_quote(symbol);
            fixture.quote_service_->push_quote(quote);
        }
        
        BENCHMARK("Get multiple quotes (1000)") {
            Core::SymbolVector symbol_vector(symbols.begin(), symbols.end());
            return fixture.quote_service_->get_quotes(symbol_vector);
        };
    }
    
    SECTION("High-frequency quote updates") {
        const size_t update_count = 10000;
        auto symbol = "HF_TEST";
        
        BENCHMARK("High-frequency quote updates (10k)") {
            for (size_t i = 0; i < update_count; ++i) {
                auto quote = fixture.generator_.generate_quote(symbol);
                fixture.quote_service_->push_quote(quote);
            }
        };
    }
}

TEST_CASE("History Service Performance", "[performance][history]") {
    PerformanceTestFixture fixture;
    
    SECTION("Bar data operations") {
        const size_t bar_count = 1000;
        Core::BarDataVector bars;
        bars.reserve(bar_count);
        
        auto now = std::chrono::system_clock::now();
        for (size_t i = 0; i < bar_count; ++i) {
            auto timestamp = now - std::chrono::minutes(bar_count - i);
            bars.push_back(fixture.generator_.generate_bar("HIST_TEST", timestamp));
        }
        
        BENCHMARK("Save 1000 bars") {
            return fixture.history_service_->save_bars(bars);
        };
        
        // Pre-populate for retrieval benchmark
        fixture.history_service_->save_bars(bars);
        
        auto start_time = now - std::chrono::minutes(bar_count);
        auto end_time = now;
        
        BENCHMARK("Retrieve 1000 bars") {
            return fixture.history_service_->get_bars(
                "HIST_TEST", Core::BarSize::OneMinute, Core::BarType::Time, start_time, end_time);
        };
    }
    
    SECTION("Large dataset operations") {
        const size_t large_bar_count = 10000;
        Core::BarDataVector large_bars;
        large_bars.reserve(large_bar_count);
        
        auto now = std::chrono::system_clock::now();
        for (size_t i = 0; i < large_bar_count; ++i) {
            auto timestamp = now - std::chrono::minutes(large_bar_count - i);
            large_bars.push_back(fixture.generator_.generate_bar("LARGE_TEST", timestamp));
        }
        
        BENCHMARK("Save 10k bars") {
            return fixture.history_service_->save_bars(large_bars);
        };
    }
}

TEST_CASE("Security Service Performance", "[performance][security]") {
    PerformanceTestFixture fixture;
    
    SECTION("Security operations") {
        auto test_security = fixture.generator_.generate_security("SEC_TEST");
        
        BENCHMARK("Add single security") {
            return fixture.security_service_->add_security(test_security);
        };
        
        // Add security for retrieval benchmark
        fixture.security_service_->add_security(test_security);
        
        BENCHMARK("Get single security") {
            return fixture.security_service_->get_security("SEC_TEST");
        };
    }
    
    SECTION("Batch security operations") {
        const size_t security_count = 1000;
        auto symbols = fixture.generator_.generate_symbols(security_count);
        
        // Pre-populate securities
        for (const auto& symbol : symbols) {
            auto security = fixture.generator_.generate_security(symbol);
            fixture.security_service_->add_security(security);
        }
        
        BENCHMARK("Get active symbols (1000)") {
            return fixture.security_service_->get_active_symbols();
        };
        
        BENCHMARK("Get symbols by market") {
            return fixture.security_service_->get_symbols_by_market(Core::Market::NYSE);
        };
        
        BENCHMARK("Get symbols by type") {
            return fixture.security_service_->get_symbols_by_type(Core::SecurityType::Stock);
        };
    }
    
    SECTION("Search operations") {
        const size_t security_count = 5000;
        auto symbols = fixture.generator_.generate_symbols(security_count);
        
        // Pre-populate with diverse securities
        for (size_t i = 0; i < symbols.size(); ++i) {
            auto security = fixture.generator_.generate_security(symbols[i]);
            
            // Vary sectors for search testing
            if (i % 3 == 0) security.sector = "Technology";
            else if (i % 3 == 1) security.sector = "Financial";
            else security.sector = "Healthcare";
            
            // Vary markets
            if (i % 2 == 0) security.market = Core::Market::NYSE;
            else security.market = Core::Market::NASDAQ;
            
            fixture.security_service_->add_security(security);
        }
        
        BENCHMARK("Search by sector (Technology)") {
            Services::SecuritySearchCriteria criteria;
            criteria.sector = "Technology";
            return fixture.security_service_->search_securities(criteria);
        };
        
        BENCHMARK("Search by market (NYSE)") {
            Services::SecuritySearchCriteria criteria;
            criteria.market = Core::Market::NYSE;
            return fixture.security_service_->search_securities(criteria);
        };
        
        BENCHMARK("Complex search with multiple criteria") {
            Services::SecuritySearchCriteria criteria;
            criteria.sector = "Technology";
            criteria.market = Core::Market::NASDAQ;
            criteria.security_type = Core::SecurityType::Stock;
            criteria.limit = 100;
            return fixture.security_service_->search_securities(criteria);
        };
    }
}

TEST_CASE("Memory Usage Benchmarks", "[performance][memory]") {
    PerformanceTestFixture fixture;
    
    SECTION("Cache performance") {
        const size_t cache_test_size = 5000;
        auto symbols = fixture.generator_.generate_symbols(cache_test_size);
        
        // Fill quote cache
        for (const auto& symbol : symbols) {
            auto quote = fixture.generator_.generate_quote(symbol);
            fixture.quote_service_->push_quote(quote);
        }
        
        BENCHMARK("Cache hit performance (5k quotes)") {
            for (const auto& symbol : symbols) {
                fixture.quote_service_->get_latest_quote(symbol);
            }
        };
        
        // Fill security cache
        for (const auto& symbol : symbols) {
            auto security = fixture.generator_.generate_security(symbol);
            fixture.security_service_->add_security(security);
        }
        
        BENCHMARK("Security cache hit performance (5k securities)") {
            for (const auto& symbol : symbols) {
                fixture.security_service_->get_security(symbol);
            }
        };
    }
}

TEST_CASE("Concurrent Access Performance", "[performance][concurrent]") {
    PerformanceTestFixture fixture;
    
    SECTION("Concurrent quote operations") {
        const size_t thread_count = 4;
        const size_t operations_per_thread = 1000;
        
        BENCHMARK("Concurrent quote push/get operations") {
            std::vector<std::thread> threads;
            
            for (size_t t = 0; t < thread_count; ++t) {
                threads.emplace_back([&fixture, t, operations_per_thread]() {
                    for (size_t i = 0; i < operations_per_thread; ++i) {
                        std::string symbol = "THREAD_" + std::to_string(t) + "_" + std::to_string(i);
                        auto quote = fixture.generator_.generate_quote(symbol);
                        
                        fixture.quote_service_->push_quote(quote);
                        fixture.quote_service_->get_latest_quote(symbol);
                    }
                });
            }
            
            for (auto& thread : threads) {
                thread.join();
            }
        };
    }
}

// Utility function to print performance summary
void print_performance_summary() {
    std::cout << "\n=== DataHub Performance Benchmark Summary ===" << std::endl;
    std::cout << "Performance benchmarks completed successfully!" << std::endl;
    std::cout << "Key performance characteristics:" << std::endl;
    std::cout << "- Quote operations: Optimized for high-frequency updates" << std::endl;
    std::cout << "- History operations: Efficient batch processing" << std::endl;
    std::cout << "- Security operations: Fast search and filtering" << std::endl;
    std::cout << "- Memory usage: Intelligent caching strategies" << std::endl;
    std::cout << "- Concurrent access: Thread-safe operations" << std::endl;
    std::cout << "=============================================" << std::endl;
}
