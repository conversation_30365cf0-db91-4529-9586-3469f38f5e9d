// EventBus Implementation - Event-driven communication system
#include "services/EventBus.h"
#include <algorithm>
#include <chrono>

namespace DataHub::Services {

// EventBus Implementation
EventBus::EventBus(EventBusConfig config)
    : config_(std::move(config))
    , running_(false)
    , stop_requested_(false)
    , event_counter_(0)
    , subscriber_counter_(0) {
}

EventBus::~EventBus() {
    if (running_.load()) {
        stop();
    }
}

EventBus::EventBus(EventBus&& other) noexcept
    : config_(std::move(other.config_))
    , running_(other.running_.load())
    , stop_requested_(other.stop_requested_.load())
    , event_queue_(std::move(other.event_queue_))
    , subscribers_(std::move(other.subscribers_))
    , worker_threads_(std::move(other.worker_threads_))
    , event_counter_(other.event_counter_.load())
    , subscriber_counter_(other.subscriber_counter_.load()) {
    
    other.running_ = false;
    other.stop_requested_ = false;
}

EventBus& EventBus::operator=(EventBus&& other) noexcept {
    if (this != &other) {
        if (running_.load()) {
            stop();
        }
        
        config_ = std::move(other.config_);
        running_ = other.running_.load();
        stop_requested_ = other.stop_requested_.load();
        event_queue_ = std::move(other.event_queue_);
        subscribers_ = std::move(other.subscribers_);
        worker_threads_ = std::move(other.worker_threads_);
        event_counter_ = other.event_counter_.load();
        subscriber_counter_ = other.subscriber_counter_.load();
        
        other.running_ = false;
        other.stop_requested_ = false;
    }
    return *this;
}

Core::Result<void> EventBus::start() {
    if (running_.load()) {
        return Core::make_error<void>(Core::ErrorCode::InvalidOperation, "EventBus already running");
    }
    
    stop_requested_ = false;
    
    // Start worker threads
    for (std::size_t i = 0; i < config_.worker_thread_count; ++i) {
        worker_threads_.emplace_back(std::make_unique<std::thread>(&EventBus::worker_loop, this));
    }
    
    running_ = true;
    
    return Core::make_success();
}

Core::Result<void> EventBus::stop() {
    if (!running_.load()) {
        return Core::make_success();
    }
    
    stop_requested_ = true;
    queue_cv_.notify_all();
    
    // Wait for all worker threads to finish
    for (auto& thread : worker_threads_) {
        if (thread && thread->joinable()) {
            thread->join();
        }
    }
    worker_threads_.clear();
    
    running_ = false;
    return Core::make_success();
}

bool EventBus::is_running() const noexcept {
    return running_.load();
}

Core::Result<std::string> EventBus::subscribe(EventType event_type, EventCallback callback) {
    if (!callback) {
        return Core::make_error<std::string>(Core::ErrorCode::InvalidArgument, "Callback cannot be null");
    }
    
    std::lock_guard<std::mutex> lock(subscribers_mutex_);
    
    std::string subscription_id = generate_subscription_id();
    
    Subscription subscription;
    subscription.id = subscription_id;
    subscription.event_type = event_type;
    subscription.callback = std::move(callback);
    subscription.created_time = std::chrono::system_clock::now();
    subscription.is_active = true;
    
    subscribers_[event_type].emplace_back(std::move(subscription));
    
    return Core::make_success(subscription_id);
}

Core::Result<void> EventBus::unsubscribe(const std::string& subscription_id) {
    std::lock_guard<std::mutex> lock(subscribers_mutex_);
    
    // Search in specific event type subscribers
    for (auto& [event_type, subscriber_list] : subscribers_) {
        auto it = std::find_if(subscriber_list.begin(), subscriber_list.end(),
            [&subscription_id](const Subscription& sub) {
                return sub.id == subscription_id;
            });
        
        if (it != subscriber_list.end()) {
            subscriber_list.erase(it);
            return Core::make_success();
        }
    }
    
    return Core::make_error<void>(Core::ErrorCode::DataNotFound, "Subscription not found");
}

Core::Result<void> EventBus::publish(const Event& event) {
    if (!running_.load()) {
        return Core::make_error<void>(Core::ErrorCode::InvalidOperation, "EventBus not running");
    }
    
    return publish_internal(event);
}

Core::Result<void> EventBus::publish_async(const Event& event) {
    if (!running_.load()) {
        return Core::make_error<void>(Core::ErrorCode::InvalidOperation, "EventBus not running");
    }
    
    std::lock_guard<std::mutex> lock(queue_mutex_);
    
    if (event_queue_.size() >= config_.max_queue_size) {
        return Core::make_error<void>(Core::ErrorCode::QueueFull, "Event queue is full");
    }
    
    event_queue_.push(event);
    queue_cv_.notify_one();
    
    return Core::make_success();
}

Core::Result<void> EventBus::clear_queue() {
    std::lock_guard<std::mutex> lock(queue_mutex_);
    
    std::queue<Event> empty_queue;
    event_queue_.swap(empty_queue);
    
    return Core::make_success();
}

Core::Result<std::size_t> EventBus::get_queue_size() const {
    std::lock_guard<std::mutex> lock(queue_mutex_);
    return Core::make_success(event_queue_.size());
}

Core::Result<std::size_t> EventBus::get_subscriber_count() const {
    std::lock_guard<std::mutex> lock(subscribers_mutex_);
    
    std::size_t total_count = 0;
    for (const auto& [event_type, subscriber_list] : subscribers_) {
        total_count += subscriber_list.size();
    }
    
    return Core::make_success(total_count);
}

Core::Result<EventBusStats> EventBus::get_statistics() const {
    EventBusStats stats;
    stats.events_published = event_counter_.load();
    stats.active_subscribers = get_subscriber_count().value_or(0);
    stats.queue_size = get_queue_size().value_or(0);
    stats.max_queue_size = config_.max_queue_size;
    stats.worker_thread_count = config_.worker_thread_count;
    stats.is_running = running_.load();
    
    return Core::make_success(stats);
}

Core::Result<void> EventBus::update_config(const EventBusConfig& config) {
    config_ = config;
    return Core::make_success();
}

Core::Result<EventBusConfig> EventBus::get_config() const {
    return Core::make_success(config_);
}

// Private methods
Core::Result<void> EventBus::publish_internal(const Event& event) {
    event_counter_++;
    
    std::lock_guard<std::mutex> lock(subscribers_mutex_);
    
    // Notify specific event type subscribers
    auto it = subscribers_.find(event.type);
    if (it != subscribers_.end()) {
        for (const auto& subscription : it->second) {
            if (subscription.is_active) {
                try {
                    subscription.callback(event);
                } catch (const std::exception& e) {
                    // Log error but continue processing
                }
            }
        }
    }
    
    return Core::make_success();
}

void EventBus::worker_loop() {
    while (!stop_requested_.load()) {
        std::unique_lock<std::mutex> lock(queue_mutex_);
        
        queue_cv_.wait(lock, [this] {
            return !event_queue_.empty() || stop_requested_.load();
        });
        
        if (stop_requested_.load()) {
            break;
        }
        
        if (!event_queue_.empty()) {
            Event event = event_queue_.front();
            event_queue_.pop();
            lock.unlock();
            
            // Process event
            publish_internal(event);
        }
    }
}

std::string EventBus::generate_subscription_id() {
    return "sub_" + std::to_string(subscriber_counter_++);
}

// Factory methods
std::unique_ptr<IEventBus> EventBusFactory::create(const EventBusConfig& config) {
    return std::make_unique<EventBus>(config);
}

std::unique_ptr<IEventBus> EventBusFactory::create_default() {
    EventBusConfig config;
    config.max_queue_size = 10000;
    config.worker_thread_count = 2;
    config.enable_async_processing = true;
    
    return create(config);
}

std::unique_ptr<IEventBus> EventBusFactory::create_high_throughput() {
    EventBusConfig config;
    config.max_queue_size = 100000;
    config.worker_thread_count = 4;
    config.enable_async_processing = true;
    
    return create(config);
}

} // namespace DataHub::Services
