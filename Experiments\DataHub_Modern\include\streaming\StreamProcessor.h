#pragma once

#include "../core/Types.h"
#include "../core/MarketData.h"
#include "../services/DataHubManager.h"
#include <functional>
#include <memory>
#include <atomic>
#include <thread>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <chrono>

namespace DataHub::Streaming {

// Stream event types
enum class StreamEventType : std::uint8_t {
    QuoteUpdate = 1,
    TickUpdate = 2,
    BarComplete = 3,
    VolumeAlert = 4,
    PriceAlert = 5,
    TechnicalSignal = 6,
    MarketOpen = 7,
    MarketClose = 8,
    Error = 9
};

// Stream event structure
struct StreamEvent {
    StreamEventType type;
    Core::Symbol symbol;
    Core::Timestamp timestamp;
    std::unordered_map<std::string, std::string> data;
    
    // Helper methods
    std::string get_data(const std::string& key, const std::string& default_value = "") const {
        auto it = data.find(key);
        return it != data.end() ? it->second : default_value;
    }
    
    void set_data(const std::string& key, const std::string& value) {
        data[key] = value;
    }
    
    template<typename T>
    void set_numeric_data(const std::string& key, T value) {
        data[key] = std::to_string(value);
    }
    
    template<typename T>
    T get_numeric_data(const std::string& key, T default_value = T{}) const {
        auto it = data.find(key);
        if (it != data.end()) {
            try {
                if constexpr (std::is_same_v<T, double>) {
                    return std::stod(it->second);
                } else if constexpr (std::is_same_v<T, float>) {
                    return std::stof(it->second);
                } else if constexpr (std::is_same_v<T, int>) {
                    return std::stoi(it->second);
                } else if constexpr (std::is_same_v<T, long>) {
                    return std::stol(it->second);
                }
            } catch (const std::exception&) {
                return default_value;
            }
        }
        return default_value;
    }
};

// Stream callback types
using StreamEventCallback = std::function<void(const StreamEvent&)>;
using QuoteStreamCallback = std::function<void(const Core::QuoteData&)>;
using TickStreamCallback = std::function<void(const Core::TickData&)>;
using BarStreamCallback = std::function<void(const Core::BarData&)>;

// Stream filter configuration
struct StreamFilter {
    std::vector<Core::Symbol> symbols;
    std::vector<StreamEventType> event_types;
    
    // Price filters
    std::optional<double> min_price;
    std::optional<double> max_price;
    std::optional<double> price_change_threshold;
    
    // Volume filters
    std::optional<Core::Volume> min_volume;
    std::optional<Core::Volume> volume_spike_threshold;
    
    // Time filters
    std::optional<Core::Timestamp> start_time;
    std::optional<Core::Timestamp> end_time;
    
    // Custom filters
    std::function<bool(const StreamEvent&)> custom_filter;
    
    bool matches(const StreamEvent& event) const {
        // Check symbol filter
        if (!symbols.empty()) {
            if (std::find(symbols.begin(), symbols.end(), event.symbol) == symbols.end()) {
                return false;
            }
        }
        
        // Check event type filter
        if (!event_types.empty()) {
            if (std::find(event_types.begin(), event_types.end(), event.type) == event_types.end()) {
                return false;
            }
        }
        
        // Check price filters
        if (min_price || max_price) {
            double price = event.get_numeric_data<double>("price", 0.0);
            if (min_price && price < *min_price) return false;
            if (max_price && price > *max_price) return false;
        }
        
        // Check volume filters
        if (min_volume) {
            auto volume = event.get_numeric_data<Core::Volume>("volume", 0);
            if (volume < *min_volume) return false;
        }
        
        // Check time filters
        if (start_time && event.timestamp < *start_time) return false;
        if (end_time && event.timestamp > *end_time) return false;
        
        // Check custom filter
        if (custom_filter && !custom_filter(event)) return false;
        
        return true;
    }
};

// Stream processor configuration
struct StreamProcessorConfig {
    // Processing settings
    std::size_t max_queue_size{10000};
    std::uint32_t processing_interval_ms{10};
    std::size_t batch_size{100};
    
    // Threading settings
    std::size_t worker_thread_count{2};
    bool enable_parallel_processing{true};
    
    // Buffer settings
    std::size_t event_buffer_size{1000};
    std::uint32_t buffer_flush_interval_ms{100};
    
    // Performance settings
    bool enable_high_frequency_mode{false};
    bool enable_zero_copy_optimization{true};
    std::size_t memory_pool_size_mb{64};
    
    // Monitoring settings
    bool enable_performance_monitoring{true};
    std::uint32_t metrics_collection_interval_ms{1000};
};

// Stream processor statistics
struct StreamProcessorStats {
    std::atomic<std::size_t> events_processed{0};
    std::atomic<std::size_t> events_filtered{0};
    std::atomic<std::size_t> events_dropped{0};
    std::atomic<std::size_t> callbacks_executed{0};
    std::atomic<std::size_t> errors_occurred{0};
    
    std::atomic<double> avg_processing_time_us{0.0};
    std::atomic<double> max_processing_time_us{0.0};
    std::atomic<double> events_per_second{0.0};
    
    Core::Timestamp start_time;
    std::atomic<std::size_t> queue_size{0};
    std::atomic<std::size_t> max_queue_size{0};
    
    void reset() {
        events_processed = 0;
        events_filtered = 0;
        events_dropped = 0;
        callbacks_executed = 0;
        errors_occurred = 0;
        avg_processing_time_us = 0.0;
        max_processing_time_us = 0.0;
        events_per_second = 0.0;
        queue_size = 0;
        max_queue_size = 0;
        start_time = std::chrono::system_clock::now();
    }
};

// Stream processor interface
class IStreamProcessor {
public:
    virtual ~IStreamProcessor() = default;
    
    // Lifecycle management
    virtual Core::Result<void> start() = 0;
    virtual Core::Result<void> stop() = 0;
    virtual bool is_running() const noexcept = 0;
    
    // Event subscription
    virtual Core::Result<std::string> subscribe(
        const StreamFilter& filter,
        StreamEventCallback callback) = 0;
    
    virtual Core::Result<std::string> subscribe_quotes(
        const Core::SymbolVector& symbols,
        QuoteStreamCallback callback) = 0;
    
    virtual Core::Result<std::string> subscribe_ticks(
        const Core::SymbolVector& symbols,
        TickStreamCallback callback) = 0;
    
    virtual Core::Result<std::string> subscribe_bars(
        const Core::SymbolVector& symbols,
        Core::BarSize bar_size,
        BarStreamCallback callback) = 0;
    
    // Unsubscription
    virtual Core::Result<void> unsubscribe(const std::string& subscription_id) = 0;
    virtual Core::Result<void> unsubscribe_all() = 0;
    
    // Event publishing
    virtual Core::Result<void> publish_event(const StreamEvent& event) = 0;
    virtual Core::Result<void> publish_quote(const Core::QuoteData& quote) = 0;
    virtual Core::Result<void> publish_tick(const Core::TickData& tick) = 0;
    virtual Core::Result<void> publish_bar(const Core::BarData& bar) = 0;
    
    // Configuration
    virtual Core::Result<void> update_config(const StreamProcessorConfig& config) = 0;
    virtual Core::Result<StreamProcessorConfig> get_config() const = 0;
    
    // Statistics and monitoring
    virtual Core::Result<StreamProcessorStats> get_statistics() const = 0;
    virtual Core::Result<void> reset_statistics() = 0;
    
    // Queue management
    virtual Core::Result<std::size_t> get_queue_size() const = 0;
    virtual Core::Result<void> clear_queue() = 0;
};

// Stream processor implementation
class StreamProcessor : public IStreamProcessor {
public:
    explicit StreamProcessor(
        std::shared_ptr<Services::IDataHubManager> datahub,
        StreamProcessorConfig config = {});
    
    ~StreamProcessor();
    
    // Disable copy, enable move
    StreamProcessor(const StreamProcessor&) = delete;
    StreamProcessor& operator=(const StreamProcessor&) = delete;
    StreamProcessor(StreamProcessor&&) noexcept;
    StreamProcessor& operator=(StreamProcessor&&) noexcept;
    
    // IStreamProcessor implementation
    Core::Result<void> start() override;
    Core::Result<void> stop() override;
    bool is_running() const noexcept override;
    
    Core::Result<std::string> subscribe(
        const StreamFilter& filter,
        StreamEventCallback callback) override;
    
    Core::Result<std::string> subscribe_quotes(
        const Core::SymbolVector& symbols,
        QuoteStreamCallback callback) override;
    
    Core::Result<std::string> subscribe_ticks(
        const Core::SymbolVector& symbols,
        TickStreamCallback callback) override;
    
    Core::Result<std::string> subscribe_bars(
        const Core::SymbolVector& symbols,
        Core::BarSize bar_size,
        BarStreamCallback callback) override;
    
    Core::Result<void> unsubscribe(const std::string& subscription_id) override;
    Core::Result<void> unsubscribe_all() override;
    
    Core::Result<void> publish_event(const StreamEvent& event) override;
    Core::Result<void> publish_quote(const Core::QuoteData& quote) override;
    Core::Result<void> publish_tick(const Core::TickData& tick) override;
    Core::Result<void> publish_bar(const Core::BarData& bar) override;
    
    Core::Result<void> update_config(const StreamProcessorConfig& config) override;
    Core::Result<StreamProcessorConfig> get_config() const override;
    
    Core::Result<StreamProcessorStats> get_statistics() const override;
    Core::Result<void> reset_statistics() override;
    
    Core::Result<std::size_t> get_queue_size() const override;
    Core::Result<void> clear_queue() override;

private:
    std::shared_ptr<Services::IDataHubManager> datahub_;
    StreamProcessorConfig config_;
    std::atomic<bool> running_;
    std::atomic<bool> stop_requested_;
    
    // Event queue
    std::queue<StreamEvent> event_queue_;
    mutable std::mutex queue_mutex_;
    std::condition_variable queue_cv_;
    
    // Subscriptions
    struct Subscription {
        std::string id;
        StreamFilter filter;
        StreamEventCallback callback;
        Core::Timestamp created_time;
    };
    
    std::unordered_map<std::string, Subscription> subscriptions_;
    mutable std::mutex subscriptions_mutex_;
    
    // Worker threads
    std::vector<std::unique_ptr<std::thread>> worker_threads_;
    
    // Statistics
    mutable StreamProcessorStats stats_;
    mutable std::mutex stats_mutex_;
    
    // Internal methods
    void worker_loop();
    void process_events();
    void process_single_event(const StreamEvent& event);
    void notify_subscribers(const StreamEvent& event);
    void update_statistics(const StreamEvent& event, 
                          std::chrono::microseconds processing_time);
    
    std::string generate_subscription_id();
    StreamEvent create_quote_event(const Core::QuoteData& quote);
    StreamEvent create_tick_event(const Core::TickData& tick);
    StreamEvent create_bar_event(const Core::BarData& bar);
    
    // Performance monitoring
    void monitor_performance();
    void collect_metrics();
};

// Factory for creating stream processors
class StreamProcessorFactory {
public:
    static std::unique_ptr<IStreamProcessor> create(
        std::shared_ptr<Services::IDataHubManager> datahub,
        const StreamProcessorConfig& config = {});
    
    static std::unique_ptr<IStreamProcessor> create_high_frequency(
        std::shared_ptr<Services::IDataHubManager> datahub,
        std::size_t max_events_per_second = 100000);
    
    static std::unique_ptr<IStreamProcessor> create_low_latency(
        std::shared_ptr<Services::IDataHubManager> datahub,
        std::chrono::microseconds max_latency = std::chrono::microseconds(100));
};

} // namespace DataHub::Streaming
