// LevelDBRepository Implementation - LevelDB key-value store repository
#include "data/LevelDBRepository.h"
#include <leveldb/db.h>
#include <leveldb/write_batch.h>
#include <leveldb/cache.h>
#include <leveldb/filter_policy.h>
#include <sstream>
#include <filesystem>
#include <chrono>

namespace DataHub::Data {

// LevelDBRepository Implementation
class LevelDBRepository::Impl {
public:
    explicit Impl(const std::string& database_path)
        : database_path_(database_path)
        , db_(nullptr)
        , connected_(false) {
    }

    ~Impl() {
        if (connected_) {
            disconnect();
        }
    }

    Core::Result<void> connect() {
        if (connected_) {
            return Core::make_success();
        }

        // Ensure directory exists
        std::filesystem::create_directories(database_path_);

        leveldb::Options options;
        options.create_if_missing = true;
        options.write_buffer_size = 64 * 1024 * 1024; // 64MB
        options.max_file_size = 64 * 1024 * 1024; // 64MB
        options.block_cache = leveldb::NewLRUCache(128 * 1024 * 1024); // 128MB cache
        options.filter_policy = leveldb::NewBloomFilterPolicy(10);

        leveldb::Status status = leveldb::DB::Open(options, database_path_, &db_);
        if (!status.ok()) {
            return Core::make_error<void>(Core::ErrorCode::DatabaseError,
                                         "Failed to open LevelDB: " + status.ToString());
        }

        connected_ = true;
        return Core::make_success();
    }

    Core::Result<void> disconnect() {
        if (!connected_ || !db_) {
            return Core::make_success();
        }

        delete db_;
        db_ = nullptr;
        connected_ = false;
        return Core::make_success();
    }

    bool is_connected() const noexcept {
        return connected_ && db_ != nullptr;
    }

    Core::Result<void> begin_transaction() {
        // LevelDB doesn't support traditional transactions
        // We'll use write batches for atomic operations
        return Core::make_success();
    }

    Core::Result<void> commit_transaction() {
        // No-op for LevelDB
        return Core::make_success();
    }

    Core::Result<void> rollback_transaction() {
        // No-op for LevelDB
        return Core::make_success();
    }

    // Quote data operations
    Core::Result<void> save_quote(const Core::QuoteData& quote) {
        if (!is_connected()) {
            return Core::make_error<void>(Core::ErrorCode::DatabaseError, "Database not connected");
        }

        std::string key = make_quote_key(quote.symbol, quote.timestamp);
        std::string value = serialize_quote(quote);

        leveldb::Status status = db_->Put(leveldb::WriteOptions(), key, value);
        if (!status.ok()) {
            return Core::make_error<void>(Core::ErrorCode::DatabaseError,
                                         "Failed to save quote: " + status.ToString());
        }

        // Also save as latest quote
        std::string latest_key = "latest_quote:" + quote.symbol;
        status = db_->Put(leveldb::WriteOptions(), latest_key, value);
        if (!status.ok()) {
            return Core::make_error<void>(Core::ErrorCode::DatabaseError,
                                         "Failed to save latest quote: " + status.ToString());
        }

        return Core::make_success();
    }

    Core::Result<Core::QuoteData> get_latest_quote(const Core::Symbol& symbol) {
        if (!is_connected()) {
            return Core::make_error<Core::QuoteData>(Core::ErrorCode::DatabaseError, "Database not connected");
        }

        std::string key = "latest_quote:" + symbol;
        std::string value;

        leveldb::Status status = db_->Get(leveldb::ReadOptions(), key, &value);
        if (status.IsNotFound()) {
            return Core::make_error<Core::QuoteData>(Core::ErrorCode::DataNotFound, "Quote not found");
        }

        if (!status.ok()) {
            return Core::make_error<Core::QuoteData>(Core::ErrorCode::DatabaseError,
                                                    "Failed to get quote: " + status.ToString());
        }

        auto quote_result = deserialize_quote(value);
        if (!quote_result.is_success()) {
            return quote_result;
        }

        return Core::make_success(quote_result.value());
    }

    // Bar data operations
    Core::Result<void> save_bar(const Core::BarData& bar) {
        if (!is_connected()) {
            return Core::make_error<void>(Core::ErrorCode::DatabaseError, "Database not connected");
        }

        std::string key = make_bar_key(bar.symbol, bar.timestamp, bar.bar_size, bar.bar_type);
        std::string value = serialize_bar(bar);

        leveldb::Status status = db_->Put(leveldb::WriteOptions(), key, value);
        if (!status.ok()) {
            return Core::make_error<void>(Core::ErrorCode::DatabaseError,
                                         "Failed to save bar: " + status.ToString());
        }

        return Core::make_success();
    }

    Core::Result<Core::BarDataVector> get_bars(const QueryCondition& condition) {
        if (!is_connected()) {
            return Core::make_error<Core::BarDataVector>(Core::ErrorCode::DatabaseError, "Database not connected");
        }

        Core::BarDataVector bars;

        // Create iterator
        leveldb::ReadOptions read_options;
        std::unique_ptr<leveldb::Iterator> it(db_->NewIterator(read_options));

        // Determine key prefix based on condition
        std::string key_prefix = "bar:";
        if (condition.symbol) {
            key_prefix += *condition.symbol + ":";
        }

        // Iterate through keys
        for (it->Seek(key_prefix); it->Valid() && it->key().starts_with(key_prefix); it->Next()) {
            auto bar_result = deserialize_bar(it->value().ToString());
            if (bar_result.is_success()) {
                const auto& bar = bar_result.value();

                // Apply filters
                if (condition.start_time && bar.timestamp < *condition.start_time) {
                    continue;
                }

                if (condition.end_time && bar.timestamp > *condition.end_time) {
                    continue;
                }

                if (condition.bar_size && bar.bar_size != *condition.bar_size) {
                    continue;
                }

                bars.push_back(bar);

                // Apply limit
                if (condition.limit && bars.size() >= *condition.limit) {
                    break;
                }
            }
        }

        if (!it->status().ok()) {
            return Core::make_error<Core::BarDataVector>(Core::ErrorCode::DatabaseError,
                                                        "Iterator error: " + it->status().ToString());
        }

        return Core::make_success(bars);
    }

    // Batch operations
    Core::Result<void> save_quotes_batch(const Core::QuoteDataVector& quotes) {
        if (!is_connected()) {
            return Core::make_error<void>(Core::ErrorCode::DatabaseError, "Database not connected");
        }

        leveldb::WriteBatch batch;

        for (const auto& quote : quotes) {
            std::string key = make_quote_key(quote.symbol, quote.timestamp);
            std::string value = serialize_quote(quote);
            batch.Put(key, value);

            // Also update latest quote
            std::string latest_key = "latest_quote:" + quote.symbol;
            batch.Put(latest_key, value);
        }

        leveldb::Status status = db_->Write(leveldb::WriteOptions(), &batch);
        if (!status.ok()) {
            return Core::make_error<void>(Core::ErrorCode::DatabaseError,
                                         "Failed to save quotes batch: " + status.ToString());
        }

        return Core::make_success();
    }

    Core::Result<void> save_bars_batch(const Core::BarDataVector& bars) {
        if (!is_connected()) {
            return Core::make_error<void>(Core::ErrorCode::DatabaseError, "Database not connected");
        }

        leveldb::WriteBatch batch;

        for (const auto& bar : bars) {
            std::string key = make_bar_key(bar.symbol, bar.timestamp, bar.bar_size, bar.bar_type);
            std::string value = serialize_bar(bar);
            batch.Put(key, value);
        }

        leveldb::Status status = db_->Write(leveldb::WriteOptions(), &batch);
        if (!status.ok()) {
            return Core::make_error<void>(Core::ErrorCode::DatabaseError,
                                         "Failed to save bars batch: " + status.ToString());
        }

        return Core::make_success();
    }

private:
    std::string database_path_;
    leveldb::DB* db_;
    bool connected_;

    // Key generation
    std::string make_quote_key(const Core::Symbol& symbol, const Core::Timestamp& timestamp) const {
        auto time_t = std::chrono::system_clock::to_time_t(timestamp);
        return "quote:" + symbol + ":" + std::to_string(time_t);
    }

    std::string make_bar_key(const Core::Symbol& symbol, const Core::Timestamp& timestamp,
                            Core::BarSize bar_size, Core::BarType bar_type) const {
        auto time_t = std::chrono::system_clock::to_time_t(timestamp);
        return "bar:" + symbol + ":" + std::to_string(time_t) + ":" +
               std::to_string(static_cast<int>(bar_size)) + ":" +
               std::to_string(static_cast<int>(bar_type));
    }

    // Serialization
    std::string serialize_quote(const Core::QuoteData& quote) const {
        std::ostringstream oss;
        auto timestamp = std::chrono::system_clock::to_time_t(quote.timestamp);

        oss << quote.symbol << "|"
            << timestamp << "|"
            << quote.bid_price << "|"
            << quote.ask_price << "|"
            << quote.bid_size << "|"
            << quote.ask_size << "|"
            << quote.last_price << "|"
            << quote.volume;

        return oss.str();
    }

    Core::Result<Core::QuoteData> deserialize_quote(const std::string& data) const {
        std::istringstream iss(data);
        std::string token;
        std::vector<std::string> tokens;

        while (std::getline(iss, token, '|')) {
            tokens.push_back(token);
        }

        if (tokens.size() != 8) {
            return Core::make_error<Core::QuoteData>(Core::ErrorCode::DataCorruption,
                                                    "Invalid quote data format");
        }

        try {
            Core::QuoteData quote;
            quote.symbol = tokens[0];
            quote.timestamp = std::chrono::system_clock::from_time_t(std::stoll(tokens[1]));
            quote.bid_price = std::stod(tokens[2]);
            quote.ask_price = std::stod(tokens[3]);
            quote.bid_size = std::stoll(tokens[4]);
            quote.ask_size = std::stoll(tokens[5]);
            quote.last_price = std::stod(tokens[6]);
            quote.volume = std::stoll(tokens[7]);

            return Core::make_success(quote);
        } catch (const std::exception& e) {
            return Core::make_error<Core::QuoteData>(Core::ErrorCode::DataCorruption,
                                                    "Failed to parse quote data: " + std::string(e.what()));
        }
    }

    std::string serialize_bar(const Core::BarData& bar) const {
        std::ostringstream oss;
        auto timestamp = std::chrono::system_clock::to_time_t(bar.timestamp);

        oss << bar.symbol << "|"
            << timestamp << "|"
            << static_cast<int>(bar.bar_size) << "|"
            << static_cast<int>(bar.bar_type) << "|"
            << bar.open << "|"
            << bar.high << "|"
            << bar.low << "|"
            << bar.close << "|"
            << bar.volume;

        return oss.str();
    }

    Core::Result<Core::BarData> deserialize_bar(const std::string& data) const {
        std::istringstream iss(data);
        std::string token;
        std::vector<std::string> tokens;

        while (std::getline(iss, token, '|')) {
            tokens.push_back(token);
        }

        if (tokens.size() != 9) {
            return Core::make_error<Core::BarData>(Core::ErrorCode::DataCorruption,
                                                  "Invalid bar data format");
        }

        try {
            Core::BarData bar;
            bar.symbol = tokens[0];
            bar.timestamp = std::chrono::system_clock::from_time_t(std::stoll(tokens[1]));
            bar.bar_size = static_cast<Core::BarSize>(std::stoi(tokens[2]));
            bar.bar_type = static_cast<Core::BarType>(std::stoi(tokens[3]));
            bar.open = std::stod(tokens[4]);
            bar.high = std::stod(tokens[5]);
            bar.low = std::stod(tokens[6]);
            bar.close = std::stod(tokens[7]);
            bar.volume = std::stoll(tokens[8]);

            return Core::make_success(bar);
        } catch (const std::exception& e) {
            return Core::make_error<Core::BarData>(Core::ErrorCode::DataCorruption,
                                                  "Failed to parse bar data: " + std::string(e.what()));
        }
    }
};

// LevelDBRepository public interface
LevelDBRepository::LevelDBRepository(const std::string& database_path)
    : pImpl_(std::make_unique<Impl>(database_path)) {
}

LevelDBRepository::~LevelDBRepository() = default;

LevelDBRepository::LevelDBRepository(LevelDBRepository&&) noexcept = default;
LevelDBRepository& LevelDBRepository::operator=(LevelDBRepository&&) noexcept = default;

Core::Result<void> LevelDBRepository::connect() {
    return pImpl_->connect();
}

Core::Result<void> LevelDBRepository::disconnect() {
    return pImpl_->disconnect();
}

bool LevelDBRepository::is_connected() const noexcept {
    return pImpl_->is_connected();
}

Core::Result<void> LevelDBRepository::begin_transaction() {
    return pImpl_->begin_transaction();
}

Core::Result<void> LevelDBRepository::commit_transaction() {
    return pImpl_->commit_transaction();
}

Core::Result<void> LevelDBRepository::rollback_transaction() {
    return pImpl_->rollback_transaction();
}

Core::Result<void> LevelDBRepository::save_quote(const Core::QuoteData& quote) {
    return pImpl_->save_quote(quote);
}

Core::Result<Core::QuoteData> LevelDBRepository::get_latest_quote(const Core::Symbol& symbol) {
    return pImpl_->get_latest_quote(symbol);
}

Core::Result<void> LevelDBRepository::save_bar(const Core::BarData& bar) {
    return pImpl_->save_bar(bar);
}

Core::Result<Core::BarDataVector> LevelDBRepository::get_bars(const QueryCondition& condition) {
    return pImpl_->get_bars(condition);
}

Core::Result<void> LevelDBRepository::save_quotes_batch(const Core::QuoteDataVector& quotes) {
    return pImpl_->save_quotes_batch(quotes);
}

Core::Result<void> LevelDBRepository::save_bars_batch(const Core::BarDataVector& bars) {
    return pImpl_->save_bars_batch(bars);
}

} // namespace DataHub::Data
