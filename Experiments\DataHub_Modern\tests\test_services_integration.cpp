// DataHub Modern - Services Integration Tests
// This file contains integration tests for the DataHub services

#include <catch2/catch_test_macros.hpp>
#include "services/QuoteService.h"
#include "services/HistoryService_Enhanced.h"
#include "services/SecurityService_Enhanced.h"
#include "data/SqliteRepository.h"
#include <chrono>
#include <memory>

using namespace DataHub;

// Test fixture for services integration
class ServicesIntegrationTest {
public:
    ServicesIntegrationTest() {
        // Create mock repositories
        quote_repo_ = std::make_shared<Data::SqliteRepository>(":memory:");
        tick_repo_ = std::make_shared<Data::SqliteRepository>(":memory:");
        bar_repo_ = std::make_shared<Data::SqliteRepository>(":memory:");
        security_repo_ = std::make_shared<Data::SqliteRepository>(":memory:");
        
        // Create services with test configurations
        Services::QuoteServiceConfig quote_config;
        quote_config.update_interval_ms = 10;
        quote_config.max_cache_size = 100;
        quote_config.enable_realtime_push = true;
        
        Services::HistoryServiceConfig history_config;
        history_config.enable_compression = false;
        history_config.max_memory_cache_mb = 10;
        history_config.batch_size = 10;
        
        Services::SecurityServiceConfig security_config;
        security_config.enable_auto_update = false;
        security_config.max_cache_size = 100;
        
        quote_service_ = std::make_unique<Services::QuoteService>(
            quote_repo_, tick_repo_, quote_config);
        
        history_service_ = std::make_unique<Services::HistoryService>(
            bar_repo_, tick_repo_, history_config);
        
        security_service_ = std::make_unique<Services::SecurityService>(
            security_repo_, security_config);
    }
    
    void setup() {
        // Start all services
        REQUIRE(quote_service_->start().is_success());
        REQUIRE(history_service_->start().is_success());
        REQUIRE(security_service_->start().is_success());
    }
    
    void teardown() {
        // Stop all services
        quote_service_->stop();
        history_service_->stop();
        security_service_->stop();
    }

protected:
    std::shared_ptr<Data::SqliteRepository> quote_repo_;
    std::shared_ptr<Data::SqliteRepository> tick_repo_;
    std::shared_ptr<Data::SqliteRepository> bar_repo_;
    std::shared_ptr<Data::SqliteRepository> security_repo_;
    
    std::unique_ptr<Services::QuoteService> quote_service_;
    std::unique_ptr<Services::HistoryService> history_service_;
    std::unique_ptr<Services::SecurityService> security_service_;
};

TEST_CASE("QuoteService Basic Operations", "[services][quote]") {
    ServicesIntegrationTest test;
    test.setup();
    
    SECTION("Service lifecycle") {
        REQUIRE(test.quote_service_->is_running());
        
        auto stop_result = test.quote_service_->stop();
        REQUIRE(stop_result.is_success());
        REQUIRE_FALSE(test.quote_service_->is_running());
        
        auto start_result = test.quote_service_->start();
        REQUIRE(start_result.is_success());
        REQUIRE(test.quote_service_->is_running());
    }
    
    SECTION("Quote data operations") {
        // Create test quote
        Core::QuoteData test_quote;
        test_quote.symbol = "TEST";
        test_quote.timestamp = std::chrono::system_clock::now();
        test_quote.bid_price = 100.0;
        test_quote.ask_price = 100.5;
        test_quote.bid_size = 100;
        test_quote.ask_size = 200;
        test_quote.last_price = 100.25;
        test_quote.volume = 1000;
        
        // Push quote
        auto push_result = test.quote_service_->push_quote(test_quote);
        REQUIRE(push_result.is_success());
        
        // Get latest quote
        auto get_result = test.quote_service_->get_latest_quote("TEST");
        REQUIRE(get_result.is_success());
        
        const auto& retrieved_quote = get_result.value();
        REQUIRE(retrieved_quote.symbol == test_quote.symbol);
        REQUIRE(retrieved_quote.last_price == test_quote.last_price);
        REQUIRE(retrieved_quote.volume == test_quote.volume);
    }
    
    SECTION("Multiple quotes") {
        std::vector<std::string> symbols = {"AAPL", "MSFT", "GOOGL"};
        
        // Push quotes for multiple symbols
        for (const auto& symbol : symbols) {
            Core::QuoteData quote;
            quote.symbol = symbol;
            quote.timestamp = std::chrono::system_clock::now();
            quote.bid_price = 100.0;
            quote.ask_price = 100.5;
            quote.last_price = 100.25;
            quote.volume = 1000;
            
            auto push_result = test.quote_service_->push_quote(quote);
            REQUIRE(push_result.is_success());
        }
        
        // Get quotes for all symbols
        Core::SymbolVector symbol_vector(symbols.begin(), symbols.end());
        auto get_result = test.quote_service_->get_quotes(symbol_vector);
        REQUIRE(get_result.is_success());
        
        const auto& quotes = get_result.value();
        REQUIRE(quotes.size() == symbols.size());
    }
    
    test.teardown();
}

TEST_CASE("HistoryService Basic Operations", "[services][history]") {
    ServicesIntegrationTest test;
    test.setup();
    
    SECTION("Service lifecycle") {
        REQUIRE(test.history_service_->is_running());
        
        auto stop_result = test.history_service_->stop();
        REQUIRE(stop_result.is_success());
        REQUIRE_FALSE(test.history_service_->is_running());
        
        auto start_result = test.history_service_->start();
        REQUIRE(start_result.is_success());
        REQUIRE(test.history_service_->is_running());
    }
    
    SECTION("Bar data operations") {
        // Create test bars
        Core::BarDataVector test_bars;
        auto now = std::chrono::system_clock::now();
        
        for (int i = 0; i < 5; ++i) {
            Core::BarData bar;
            bar.symbol = "TEST";
            bar.timestamp = now - std::chrono::minutes(5 - i);
            bar.bar_size = Core::BarSize::OneMinute;
            bar.bar_type = Core::BarType::Time;
            bar.open = 100.0 + i;
            bar.high = bar.open + 1.0;
            bar.low = bar.open - 0.5;
            bar.close = bar.open + 0.5;
            bar.volume = 1000 + i * 100;
            
            test_bars.push_back(bar);
        }
        
        // Save bars
        auto save_result = test.history_service_->save_bars(test_bars);
        REQUIRE(save_result.is_success());
        
        // Retrieve bars
        auto start_time = now - std::chrono::minutes(10);
        auto end_time = now;
        
        auto get_result = test.history_service_->get_bars(
            "TEST", Core::BarSize::OneMinute, Core::BarType::Time, start_time, end_time);
        REQUIRE(get_result.is_success());
        
        const auto& retrieved_bars = get_result.value();
        REQUIRE(retrieved_bars.size() == test_bars.size());
        
        // Verify first and last bars
        REQUIRE(retrieved_bars.front().open == test_bars.front().open);
        REQUIRE(retrieved_bars.back().close == test_bars.back().close);
    }
    
    test.teardown();
}

TEST_CASE("SecurityService Basic Operations", "[services][security]") {
    ServicesIntegrationTest test;
    test.setup();
    
    SECTION("Service lifecycle") {
        REQUIRE(test.security_service_->is_running());
        
        auto stop_result = test.security_service_->stop();
        REQUIRE(stop_result.is_success());
        REQUIRE_FALSE(test.security_service_->is_running());
        
        auto start_result = test.security_service_->start();
        REQUIRE(start_result.is_success());
        REQUIRE(test.security_service_->is_running());
    }
    
    SECTION("Security information operations") {
        // Create test security
        Core::SecurityInfo test_security;
        test_security.symbol = "TEST";
        test_security.name = "Test Corporation";
        test_security.type = Core::SecurityType::Stock;
        test_security.market = Core::Market::NYSE;
        test_security.currency = Core::Currency::USD;
        test_security.sector = "Technology";
        test_security.industry = "Software";
        test_security.is_active = true;
        
        // Add security
        auto add_result = test.security_service_->add_security(test_security);
        REQUIRE(add_result.is_success());
        
        // Get security
        auto get_result = test.security_service_->get_security("TEST");
        REQUIRE(get_result.is_success());
        
        const auto& retrieved_security = get_result.value();
        REQUIRE(retrieved_security.symbol == test_security.symbol);
        REQUIRE(retrieved_security.name == test_security.name);
        REQUIRE(retrieved_security.type == test_security.type);
        REQUIRE(retrieved_security.market == test_security.market);
    }
    
    SECTION("Security search operations") {
        // Add multiple securities
        std::vector<Core::SecurityInfo> securities = {
            {"AAPL", "Apple Inc.", Core::SecurityType::Stock, Core::Market::NASDAQ, Core::Currency::USD, "Technology", "Consumer Electronics", true},
            {"MSFT", "Microsoft Corporation", Core::SecurityType::Stock, Core::Market::NASDAQ, Core::Currency::USD, "Technology", "Software", true},
            {"JPM", "JPMorgan Chase & Co.", Core::SecurityType::Stock, Core::Market::NYSE, Core::Currency::USD, "Financial", "Banking", true}
        };
        
        for (const auto& security : securities) {
            auto add_result = test.security_service_->add_security(security);
            REQUIRE(add_result.is_success());
        }
        
        // Search by sector
        Services::SecuritySearchCriteria criteria;
        criteria.sector = "Technology";
        
        auto search_result = test.security_service_->search_securities(criteria);
        REQUIRE(search_result.is_success());
        
        const auto& found_securities = search_result.value();
        REQUIRE(found_securities.size() == 2); // AAPL and MSFT
        
        // Verify all found securities are in Technology sector
        for (const auto& security : found_securities) {
            REQUIRE(security.sector == "Technology");
        }
    }
    
    SECTION("Market and type filtering") {
        // Add securities to different markets
        Core::SecurityInfo nasdaq_stock = {"NDAQ", "NASDAQ Inc.", Core::SecurityType::Stock, Core::Market::NASDAQ, Core::Currency::USD, "Financial", "Exchange", true};
        Core::SecurityInfo nyse_stock = {"ICE", "Intercontinental Exchange", Core::SecurityType::Stock, Core::Market::NYSE, Core::Currency::USD, "Financial", "Exchange", true};
        
        test.security_service_->add_security(nasdaq_stock);
        test.security_service_->add_security(nyse_stock);
        
        // Get symbols by market
        auto nasdaq_symbols_result = test.security_service_->get_symbols_by_market(Core::Market::NASDAQ);
        REQUIRE(nasdaq_symbols_result.is_success());
        
        const auto& nasdaq_symbols = nasdaq_symbols_result.value();
        REQUIRE(std::find(nasdaq_symbols.begin(), nasdaq_symbols.end(), "NDAQ") != nasdaq_symbols.end());
        
        auto nyse_symbols_result = test.security_service_->get_symbols_by_market(Core::Market::NYSE);
        REQUIRE(nyse_symbols_result.is_success());
        
        const auto& nyse_symbols = nyse_symbols_result.value();
        REQUIRE(std::find(nyse_symbols.begin(), nyse_symbols.end(), "ICE") != nyse_symbols.end());
        
        // Get symbols by type
        auto stock_symbols_result = test.security_service_->get_symbols_by_type(Core::SecurityType::Stock);
        REQUIRE(stock_symbols_result.is_success());
        
        const auto& stock_symbols = stock_symbols_result.value();
        REQUIRE(stock_symbols.size() >= 2); // At least NDAQ and ICE
    }
    
    test.teardown();
}

TEST_CASE("Services Integration", "[services][integration]") {
    ServicesIntegrationTest test;
    test.setup();
    
    SECTION("Cross-service data consistency") {
        // Add security first
        Core::SecurityInfo security;
        security.symbol = "INTEG";
        security.name = "Integration Test Corp";
        security.type = Core::SecurityType::Stock;
        security.market = Core::Market::NYSE;
        security.currency = Core::Currency::USD;
        security.sector = "Technology";
        security.is_active = true;
        
        auto add_security_result = test.security_service_->add_security(security);
        REQUIRE(add_security_result.is_success());
        
        // Add quote for the same symbol
        Core::QuoteData quote;
        quote.symbol = "INTEG";
        quote.timestamp = std::chrono::system_clock::now();
        quote.bid_price = 50.0;
        quote.ask_price = 50.5;
        quote.last_price = 50.25;
        quote.volume = 1000;
        
        auto push_quote_result = test.quote_service_->push_quote(quote);
        REQUIRE(push_quote_result.is_success());
        
        // Add historical data for the same symbol
        Core::BarDataVector bars;
        auto now = std::chrono::system_clock::now();
        
        Core::BarData bar;
        bar.symbol = "INTEG";
        bar.timestamp = now - std::chrono::minutes(1);
        bar.bar_size = Core::BarSize::OneMinute;
        bar.bar_type = Core::BarType::Time;
        bar.open = 50.0;
        bar.high = 50.5;
        bar.low = 49.8;
        bar.close = 50.2;
        bar.volume = 500;
        
        bars.push_back(bar);
        
        auto save_bars_result = test.history_service_->save_bars(bars);
        REQUIRE(save_bars_result.is_success());
        
        // Verify all data is consistent
        auto get_security_result = test.security_service_->get_security("INTEG");
        REQUIRE(get_security_result.is_success());
        REQUIRE(get_security_result.value().symbol == "INTEG");
        
        auto get_quote_result = test.quote_service_->get_latest_quote("INTEG");
        REQUIRE(get_quote_result.is_success());
        REQUIRE(get_quote_result.value().symbol == "INTEG");
        
        auto get_bars_result = test.history_service_->get_bars(
            "INTEG", Core::BarSize::OneMinute, Core::BarType::Time, 
            now - std::chrono::minutes(5), now);
        REQUIRE(get_bars_result.is_success());
        REQUIRE(get_bars_result.value().size() == 1);
        REQUIRE(get_bars_result.value()[0].symbol == "INTEG");
    }
    
    test.teardown();
}

TEST_CASE("Error Handling", "[services][error]") {
    ServicesIntegrationTest test;
    test.setup();
    
    SECTION("Invalid data handling") {
        // Test invalid quote data
        Core::QuoteData invalid_quote;
        invalid_quote.symbol = ""; // Empty symbol
        invalid_quote.bid_price = -1.0; // Negative price
        
        auto push_result = test.quote_service_->push_quote(invalid_quote);
        REQUIRE_FALSE(push_result.is_success());
        REQUIRE(push_result.error().code == Core::ErrorCode::InvalidArgument);
        
        // Test invalid security data
        Core::SecurityInfo invalid_security;
        invalid_security.symbol = ""; // Empty symbol
        invalid_security.type = Core::SecurityType::Unknown;
        
        auto add_result = test.security_service_->add_security(invalid_security);
        REQUIRE_FALSE(add_result.is_success());
        REQUIRE(add_result.error().code == Core::ErrorCode::InvalidArgument);
    }
    
    SECTION("Non-existent data queries") {
        // Query non-existent quote
        auto quote_result = test.quote_service_->get_latest_quote("NONEXISTENT");
        REQUIRE_FALSE(quote_result.is_success());
        
        // Query non-existent security
        auto security_result = test.security_service_->get_security("NONEXISTENT");
        REQUIRE_FALSE(security_result.is_success());
        
        // Query non-existent historical data
        auto now = std::chrono::system_clock::now();
        auto bars_result = test.history_service_->get_bars(
            "NONEXISTENT", Core::BarSize::OneMinute, Core::BarType::Time,
            now - std::chrono::hours(1), now);
        // This might succeed but return empty results, depending on implementation
    }
    
    test.teardown();
}
