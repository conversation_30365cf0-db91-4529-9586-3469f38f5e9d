// IndicatorService Implementation - Technical indicators calculation service
#include "services/IndicatorService.h"
#include <algorithm>
#include <numeric>
#include <cmath>

namespace DataHub::Services {

// IndicatorService Implementation
IndicatorService::IndicatorService(
    std::shared_ptr<IHistoryService> history_service,
    IndicatorServiceConfig config)
    : history_service_(std::move(history_service))
    , config_(std::move(config))
    , running_(false) {
}

IndicatorService::~IndicatorService() {
    if (running_.load()) {
        stop();
    }
}

Core::Result<void> IndicatorService::start() {
    if (running_.load()) {
        return Core::make_error<void>(Core::ErrorCode::InvalidOperation, "IndicatorService already running");
    }
    
    if (!history_service_) {
        return Core::make_error<void>(Core::ErrorCode::InvalidOperation, "History service not available");
    }
    
    running_ = true;
    return Core::make_success();
}

Core::Result<void> IndicatorService::stop() {
    running_ = false;
    
    // Clear cache if needed
    if (config_.clear_cache_on_stop) {
        std::lock_guard<std::mutex> lock(cache_mutex_);
        indicator_cache_.clear();
    }
    
    return Core::make_success();
}

bool IndicatorService::is_running() const noexcept {
    return running_.load();
}

// Simple Moving Average
Core::Result<IndicatorResultVector> IndicatorService::calculate_sma(
    const Core::Symbol& symbol,
    std::size_t period,
    const Core::Timestamp& start_time,
    const Core::Timestamp& end_time) {
    
    if (!running_.load()) {
        return Core::make_error<IndicatorResultVector>(Core::ErrorCode::ServiceNotAvailable, "Service not running");
    }
    
    if (period == 0) {
        return Core::make_error<IndicatorResultVector>(Core::ErrorCode::InvalidArgument, "Period cannot be zero");
    }
    
    // Check cache first
    std::string cache_key = make_cache_key("SMA", symbol, period, start_time, end_time);
    if (auto cached_result = get_from_cache(cache_key)) {
        return Core::make_success(*cached_result);
    }
    
    // Get historical data
    auto bars_result = history_service_->get_bars(symbol, Core::BarSize::OneDay, Core::BarType::Time, start_time, end_time);
    if (!bars_result.is_success()) {
        return Core::make_error<IndicatorResultVector>(bars_result.error().code, bars_result.error().message);
    }
    
    const auto& bars = bars_result.value();
    if (bars.size() < period) {
        return Core::make_error<IndicatorResultVector>(Core::ErrorCode::InsufficientData, "Not enough data for SMA calculation");
    }
    
    IndicatorResultVector results;
    results.reserve(bars.size() - period + 1);
    
    // Calculate SMA
    for (std::size_t i = period - 1; i < bars.size(); ++i) {
        double sum = 0.0;
        for (std::size_t j = i - period + 1; j <= i; ++j) {
            sum += bars[j].close;
        }
        double sma = sum / static_cast<double>(period);
        
        IndicatorResult result;
        result.type = IndicatorType::SMA;
        result.symbol = symbol;
        result.timestamp = bars[i].timestamp;
        result.values["sma"] = sma;
        result.values["period"] = static_cast<double>(period);
        
        results.push_back(result);
    }
    
    // Cache result
    cache_result(cache_key, results);
    
    return Core::make_success(results);
}

// Exponential Moving Average
Core::Result<IndicatorResultVector> IndicatorService::calculate_ema(
    const Core::Symbol& symbol,
    std::size_t period,
    const Core::Timestamp& start_time,
    const Core::Timestamp& end_time) {
    
    if (!running_.load()) {
        return Core::make_error<IndicatorResultVector>(Core::ErrorCode::ServiceNotAvailable, "Service not running");
    }
    
    if (period == 0) {
        return Core::make_error<IndicatorResultVector>(Core::ErrorCode::InvalidArgument, "Period cannot be zero");
    }
    
    // Check cache first
    std::string cache_key = make_cache_key("EMA", symbol, period, start_time, end_time);
    if (auto cached_result = get_from_cache(cache_key)) {
        return Core::make_success(*cached_result);
    }
    
    // Get historical data
    auto bars_result = history_service_->get_bars(symbol, Core::BarSize::OneDay, Core::BarType::Time, start_time, end_time);
    if (!bars_result.is_success()) {
        return Core::make_error<IndicatorResultVector>(bars_result.error().code, bars_result.error().message);
    }
    
    const auto& bars = bars_result.value();
    if (bars.size() < period) {
        return Core::make_error<IndicatorResultVector>(Core::ErrorCode::InsufficientData, "Not enough data for EMA calculation");
    }
    
    IndicatorResultVector results;
    results.reserve(bars.size());
    
    // Calculate smoothing factor
    double alpha = 2.0 / (static_cast<double>(period) + 1.0);
    
    // Initialize with first SMA
    double sum = 0.0;
    for (std::size_t i = 0; i < period; ++i) {
        sum += bars[i].close;
    }
    double ema = sum / static_cast<double>(period);
    
    // Add first EMA result
    IndicatorResult first_result;
    first_result.type = IndicatorType::EMA;
    first_result.symbol = symbol;
    first_result.timestamp = bars[period - 1].timestamp;
    first_result.values["ema"] = ema;
    first_result.values["period"] = static_cast<double>(period);
    results.push_back(first_result);
    
    // Calculate subsequent EMAs
    for (std::size_t i = period; i < bars.size(); ++i) {
        ema = alpha * bars[i].close + (1.0 - alpha) * ema;
        
        IndicatorResult result;
        result.type = IndicatorType::EMA;
        result.symbol = symbol;
        result.timestamp = bars[i].timestamp;
        result.values["ema"] = ema;
        result.values["period"] = static_cast<double>(period);
        
        results.push_back(result);
    }
    
    // Cache result
    cache_result(cache_key, results);
    
    return Core::make_success(results);
}

// Relative Strength Index
Core::Result<IndicatorResultVector> IndicatorService::calculate_rsi(
    const Core::Symbol& symbol,
    std::size_t period,
    const Core::Timestamp& start_time,
    const Core::Timestamp& end_time) {
    
    if (!running_.load()) {
        return Core::make_error<IndicatorResultVector>(Core::ErrorCode::ServiceNotAvailable, "Service not running");
    }
    
    if (period == 0) {
        return Core::make_error<IndicatorResultVector>(Core::ErrorCode::InvalidArgument, "Period cannot be zero");
    }
    
    // Check cache first
    std::string cache_key = make_cache_key("RSI", symbol, period, start_time, end_time);
    if (auto cached_result = get_from_cache(cache_key)) {
        return Core::make_success(*cached_result);
    }
    
    // Get historical data
    auto bars_result = history_service_->get_bars(symbol, Core::BarSize::OneDay, Core::BarType::Time, start_time, end_time);
    if (!bars_result.is_success()) {
        return Core::make_error<IndicatorResultVector>(bars_result.error().code, bars_result.error().message);
    }
    
    const auto& bars = bars_result.value();
    if (bars.size() < period + 1) {
        return Core::make_error<IndicatorResultVector>(Core::ErrorCode::InsufficientData, "Not enough data for RSI calculation");
    }
    
    IndicatorResultVector results;
    results.reserve(bars.size() - period);
    
    // Calculate price changes
    std::vector<double> gains, losses;
    for (std::size_t i = 1; i < bars.size(); ++i) {
        double change = bars[i].close - bars[i-1].close;
        gains.push_back(change > 0 ? change : 0.0);
        losses.push_back(change < 0 ? -change : 0.0);
    }
    
    // Calculate initial average gain and loss
    double avg_gain = 0.0, avg_loss = 0.0;
    for (std::size_t i = 0; i < period; ++i) {
        avg_gain += gains[i];
        avg_loss += losses[i];
    }
    avg_gain /= static_cast<double>(period);
    avg_loss /= static_cast<double>(period);
    
    // Calculate RSI for each subsequent period
    for (std::size_t i = period; i < gains.size(); ++i) {
        // Smoothed averages
        avg_gain = (avg_gain * (period - 1) + gains[i]) / static_cast<double>(period);
        avg_loss = (avg_loss * (period - 1) + losses[i]) / static_cast<double>(period);
        
        double rs = avg_loss == 0.0 ? 100.0 : avg_gain / avg_loss;
        double rsi = 100.0 - (100.0 / (1.0 + rs));
        
        IndicatorResult result;
        result.type = IndicatorType::RSI;
        result.symbol = symbol;
        result.timestamp = bars[i + 1].timestamp;
        result.values["rsi"] = rsi;
        result.values["period"] = static_cast<double>(period);
        result.values["avg_gain"] = avg_gain;
        result.values["avg_loss"] = avg_loss;
        
        results.push_back(result);
    }
    
    // Cache result
    cache_result(cache_key, results);
    
    return Core::make_success(results);
}

// Configuration management
Core::Result<void> IndicatorService::update_config(const IndicatorServiceConfig& config) {
    config_ = config;
    return Core::make_success();
}

Core::Result<IndicatorServiceConfig> IndicatorService::get_config() const {
    return Core::make_success(config_);
}

// Cache management
Core::Result<void> IndicatorService::clear_cache() {
    std::lock_guard<std::mutex> lock(cache_mutex_);
    indicator_cache_.clear();
    return Core::make_success();
}

Core::Result<std::size_t> IndicatorService::get_cache_size() const {
    std::lock_guard<std::mutex> lock(cache_mutex_);
    return Core::make_success(indicator_cache_.size());
}

// Private helper methods
std::string IndicatorService::make_cache_key(
    const std::string& indicator_type,
    const Core::Symbol& symbol,
    std::size_t period,
    const Core::Timestamp& start_time,
    const Core::Timestamp& end_time) const {
    
    auto start_time_t = std::chrono::system_clock::to_time_t(start_time);
    auto end_time_t = std::chrono::system_clock::to_time_t(end_time);
    
    return indicator_type + "_" + symbol + "_" + std::to_string(period) + "_" +
           std::to_string(start_time_t) + "_" + std::to_string(end_time_t);
}

std::optional<IndicatorResultVector> IndicatorService::get_from_cache(const std::string& key) const {
    if (!config_.enable_cache) {
        return std::nullopt;
    }
    
    std::lock_guard<std::mutex> lock(cache_mutex_);
    auto it = indicator_cache_.find(key);
    if (it != indicator_cache_.end()) {
        return it->second;
    }
    return std::nullopt;
}

void IndicatorService::cache_result(const std::string& key, const IndicatorResultVector& result) {
    if (!config_.enable_cache) {
        return;
    }
    
    std::lock_guard<std::mutex> lock(cache_mutex_);
    
    // Check cache size limit
    if (indicator_cache_.size() >= config_.max_cache_size) {
        // Simple LRU: remove first element
        indicator_cache_.erase(indicator_cache_.begin());
    }
    
    indicator_cache_[key] = result;
}

} // namespace DataHub::Services
