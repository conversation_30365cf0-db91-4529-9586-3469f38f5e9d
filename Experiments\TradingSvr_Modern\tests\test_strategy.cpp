/**
 * @file test_strategy.cpp
 * @brief Unit tests for Strategy classes
 * <AUTHOR> Team
 * @date 2024
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "trading/Strategy.h"
#include "trading/strategies/FuturesStrategy.h"
#include "trading/strategies/StockStrategy.h"

using namespace RoboQuant::Trading;
using namespace RoboQuant::Trading::Strategies;
using ::testing::_;
using ::testing::Return;
using ::testing::StrictMock;

// Mock classes for testing
class MockOrderManager : public OrderManager {
public:
    MockOrderManager() : OrderManager(nullptr, nullptr) {}
    
    MOCK_METHOD(std::future<std::optional<OrderId>>, submit_order, (const OrderRequest&), (override));
    MOCK_METHOD(std::future<bool>, cancel_order, (const OrderId&), (override));
    MOCK_METHOD(std::optional<Order>, get_order, (const OrderId&), (const, override));
};

class MockDataProvider : public DataProvider {
public:
    MOCK_METHOD(std::future<std::optional<QuoteData>>, get_quote, (const AssetId&), (override));
    MOCK_METHOD(std::future<std::vector<BarData>>, get_bars, 
                (const AssetId&, TimePoint, TimePoint, Duration), (override));
    MOCK_METHOD(std::unique_ptr<DataSubscription>, subscribe_quotes, 
                (const std::vector<AssetId>&, QuoteCallback), (override));
    MOCK_METHOD(std::unique_ptr<DataSubscription>, subscribe_bars, 
                (const std::vector<AssetId>&, Duration, BarCallback), (override));
    MOCK_METHOD(std::future<std::vector<AssetId>>, get_available_assets, (), (override));
    MOCK_METHOD(std::future<bool>, is_asset_available, (const AssetId&), (override));
    MOCK_METHOD(std::future<bool>, connect, (), (override));
    MOCK_METHOD(std::future<void>, disconnect, (), (override));
    MOCK_METHOD(bool, is_connected, (), (const, override));
    MOCK_METHOD(std::string, get_provider_name, (), (const, override));
    MOCK_METHOD(std::unordered_map<std::string, std::string>, get_status, (), (const, override));
};

class MockPortfolio : public Portfolio {
public:
    MockPortfolio() : Portfolio(PortfolioConfig{}) {}
    
    MOCK_METHOD(Amount, get_cash, (), (const, override));
    MOCK_METHOD(void, add_cash, (Amount), (override));
    MOCK_METHOD(bool, has_sufficient_cash, (Amount), (const, override));
    MOCK_METHOD(std::optional<Position>, get_position, (const AssetId&), (const, override));
    MOCK_METHOD(void, update_position, (const AssetId&, Quantity, Price, Amount), (override));
    MOCK_METHOD(bool, check_risk_limits, (const OrderRequest&, Price), (const, override));
};

// Test strategy implementation
class TestStrategy : public Strategy {
public:
    explicit TestStrategy(StrategyConfig config) : Strategy(std::move(config)) {}
    
    std::future<bool> initialize() override {
        std::promise<bool> promise;
        promise.set_value(true);
        return promise.get_future();
    }
    
    std::future<void> start() override {
        set_state(StrategyState::Running);
        std::promise<void> promise;
        promise.set_value();
        return promise.get_future();
    }
    
    std::future<void> stop() override {
        set_state(StrategyState::Stopped);
        std::promise<void> promise;
        promise.set_value();
        return promise.get_future();
    }
    
    void shutdown() override {
        set_state(StrategyState::Stopped);
    }
    
    // Test access to protected methods
    void test_set_state(StrategyState state) { set_state(state); }
    bool test_can_trade() const { return can_trade(); }
    bool test_check_risk_limits(const OrderRequest& request) const {
        return check_risk_limits(request);
    }
};

class StrategyTest : public ::testing::Test {
protected:
    void SetUp() override {
        config_.id = "test_strategy";
        config_.name = "Test Strategy";
        config_.description = "Strategy for unit testing";
        config_.enabled = true;
        
        strategy_ = std::make_unique<TestStrategy>(config_);
    }

    StrategyConfig config_;
    std::unique_ptr<TestStrategy> strategy_;
};

TEST_F(StrategyTest, InitialState) {
    EXPECT_EQ(strategy_->get_config().id, "test_strategy");
    EXPECT_EQ(strategy_->get_config().name, "Test Strategy");
    EXPECT_EQ(strategy_->get_state(), StrategyState::Stopped);
    EXPECT_TRUE(strategy_->is_enabled());
}

TEST_F(StrategyTest, StateManagement) {
    // Test state transitions
    strategy_->test_set_state(StrategyState::Starting);
    EXPECT_EQ(strategy_->get_state(), StrategyState::Starting);
    
    strategy_->test_set_state(StrategyState::Running);
    EXPECT_EQ(strategy_->get_state(), StrategyState::Running);
    
    strategy_->test_set_state(StrategyState::Stopping);
    EXPECT_EQ(strategy_->get_state(), StrategyState::Stopping);
    
    strategy_->test_set_state(StrategyState::Stopped);
    EXPECT_EQ(strategy_->get_state(), StrategyState::Stopped);
}

TEST_F(StrategyTest, EnableDisable) {
    EXPECT_TRUE(strategy_->is_enabled());
    
    strategy_->disable();
    EXPECT_FALSE(strategy_->is_enabled());
    
    strategy_->enable();
    EXPECT_TRUE(strategy_->is_enabled());
}

TEST_F(StrategyTest, CanTrade) {
    // Should not be able to trade when stopped
    EXPECT_FALSE(strategy_->test_can_trade());
    
    // Should be able to trade when running and enabled
    strategy_->test_set_state(StrategyState::Running);
    EXPECT_TRUE(strategy_->test_can_trade());
    
    // Should not be able to trade when disabled
    strategy_->disable();
    EXPECT_FALSE(strategy_->test_can_trade());
}

TEST_F(StrategyTest, RiskLimits) {
    RiskLimits limits;
    limits.max_position_value = 100000.0;
    limits.max_daily_loss = 5000.0;
    
    strategy_->set_risk_limits(limits);
    
    auto retrieved_limits = strategy_->get_risk_limits();
    EXPECT_EQ(retrieved_limits.max_position_value, 100000.0);
    EXPECT_EQ(retrieved_limits.max_daily_loss, 5000.0);
}

TEST_F(StrategyTest, ComponentManagement) {
    auto mock_portfolio = std::make_shared<MockPortfolio>();
    auto mock_order_manager = std::make_shared<MockOrderManager>();
    auto mock_data_provider = std::make_shared<MockDataProvider>();
    
    strategy_->set_portfolio(mock_portfolio);
    strategy_->set_order_manager(mock_order_manager);
    strategy_->set_data_provider(mock_data_provider);
    
    EXPECT_EQ(strategy_->get_portfolio().lock(), mock_portfolio);
    EXPECT_EQ(strategy_->get_order_manager().lock(), mock_order_manager);
    EXPECT_EQ(strategy_->get_data_provider().lock(), mock_data_provider);
}

TEST_F(StrategyTest, Lifecycle) {
    // Test initialization
    auto init_future = strategy_->initialize();
    EXPECT_TRUE(init_future.get());
    
    // Test start
    auto start_future = strategy_->start();
    start_future.wait();
    EXPECT_EQ(strategy_->get_state(), StrategyState::Running);
    
    // Test stop
    auto stop_future = strategy_->stop();
    stop_future.wait();
    EXPECT_EQ(strategy_->get_state(), StrategyState::Stopped);
    
    // Test shutdown
    strategy_->shutdown();
    EXPECT_EQ(strategy_->get_state(), StrategyState::Stopped);
}

// Test futures strategy
class FuturesStrategyTest : public ::testing::Test {
protected:
    void SetUp() override {
        config_.id = "futures_test";
        config_.name = "Futures Test Strategy";
        config_.description = "Test futures strategy";
        config_.enabled = true;
        
        // Add some test parameters
        config_.parameters["entry_threshold"] = 0.02;
        config_.parameters["stop_loss_pct"] = 0.05;
        config_.parameters["max_position_size"] = 1000000.0;
        
        strategy_ = std::make_unique<FuturesStrategy>(config_);
    }

    StrategyConfig config_;
    std::unique_ptr<FuturesStrategy> strategy_;
};

TEST_F(FuturesStrategyTest, Configuration) {
    EXPECT_EQ(strategy_->get_config().id, "futures_test");
    EXPECT_EQ(FuturesStrategy::strategy_name(), "FuturesStrategy");
    
    // Test parameter access
    auto entry_threshold = strategy_->get_config().get_parameter<double>("entry_threshold");
    ASSERT_TRUE(entry_threshold.has_value());
    EXPECT_EQ(*entry_threshold, 0.02);
}

TEST_F(FuturesStrategyTest, FuturesConfig) {
    FuturesStrategyConfig futures_config;
    futures_config.entry_threshold = 0.03;
    futures_config.exit_threshold = 0.015;
    futures_config.stop_loss_pct = 0.06;
    
    strategy_->set_futures_config(futures_config);
    
    auto retrieved_config = strategy_->get_futures_config();
    EXPECT_EQ(retrieved_config.entry_threshold, 0.03);
    EXPECT_EQ(retrieved_config.exit_threshold, 0.015);
    EXPECT_EQ(retrieved_config.stop_loss_pct, 0.06);
}

// Test stock strategy
class StockStrategyTest : public ::testing::Test {
protected:
    void SetUp() override {
        config_.id = "stock_test";
        config_.name = "Stock Test Strategy";
        config_.description = "Test stock strategy";
        config_.enabled = true;
        
        // Add some test parameters
        config_.parameters["long_entry_threshold"] = 0.02;
        config_.parameters["stop_loss_pct"] = 0.08;
        config_.parameters["max_position_value"] = 100000.0;
        
        strategy_ = std::make_unique<StockStrategy>(config_);
    }

    StrategyConfig config_;
    std::unique_ptr<StockStrategy> strategy_;
};

TEST_F(StockStrategyTest, Configuration) {
    EXPECT_EQ(strategy_->get_config().id, "stock_test");
    EXPECT_EQ(StockStrategy::strategy_name(), "StockStrategy");
    
    // Test parameter access
    auto entry_threshold = strategy_->get_config().get_parameter<double>("long_entry_threshold");
    ASSERT_TRUE(entry_threshold.has_value());
    EXPECT_EQ(*entry_threshold, 0.02);
}

TEST_F(StockStrategyTest, StockConfig) {
    StockStrategyConfig stock_config;
    stock_config.long_entry_threshold = 0.025;
    stock_config.long_exit_threshold = 0.012;
    stock_config.stop_loss_pct = 0.09;
    
    strategy_->set_stock_config(stock_config);
    
    auto retrieved_config = strategy_->get_stock_config();
    EXPECT_EQ(retrieved_config.long_entry_threshold, 0.025);
    EXPECT_EQ(retrieved_config.long_exit_threshold, 0.012);
    EXPECT_EQ(retrieved_config.stop_loss_pct, 0.09);
}

// Test strategy manager
class StrategyManagerTest : public ::testing::Test {
protected:
    void SetUp() override {
        manager_ = std::make_unique<StrategyManager>();
    }

    std::unique_ptr<StrategyManager> manager_;
};

TEST_F(StrategyManagerTest, StrategyManagement) {
    StrategyConfig config;
    config.id = "test_strategy_1";
    config.name = "Test Strategy 1";
    
    auto strategy = std::make_unique<TestStrategy>(config);
    auto* strategy_ptr = strategy.get();
    
    // Add strategy
    auto add_future = manager_->add_strategy(std::move(strategy));
    EXPECT_TRUE(add_future.get());
    
    // Get strategy
    auto* retrieved_strategy = manager_->get_strategy("test_strategy_1");
    EXPECT_EQ(retrieved_strategy, strategy_ptr);
    
    // Get all strategies
    auto all_strategies = manager_->get_all_strategies();
    EXPECT_EQ(all_strategies.size(), 1);
    EXPECT_EQ(all_strategies[0], strategy_ptr);
    
    // Remove strategy
    auto remove_future = manager_->remove_strategy("test_strategy_1");
    EXPECT_TRUE(remove_future.get());
    
    // Verify removal
    EXPECT_EQ(manager_->get_strategy("test_strategy_1"), nullptr);
    EXPECT_EQ(manager_->get_all_strategies().size(), 0);
}

TEST_F(StrategyManagerTest, EventDistribution) {
    // Add test strategies
    StrategyConfig config1;
    config1.id = "strategy_1";
    auto strategy1 = std::make_unique<TestStrategy>(config1);
    
    StrategyConfig config2;
    config2.id = "strategy_2";
    auto strategy2 = std::make_unique<TestStrategy>(config2);
    
    auto add_future1 = manager_->add_strategy(std::move(strategy1));
    auto add_future2 = manager_->add_strategy(std::move(strategy2));
    
    EXPECT_TRUE(add_future1.get());
    EXPECT_TRUE(add_future2.get());
    
    // Test event distribution (this would require more sophisticated mocking)
    QuoteData quote;
    quote.asset_id = "AAPL";
    quote.timestamp = std::chrono::system_clock::now();
    quote.last_price = 150.0;
    
    // This should not throw
    EXPECT_NO_THROW(manager_->distribute_market_data(quote));
    
    BarData bar;
    bar.asset_id = "GOOGL";
    bar.timestamp = std::chrono::system_clock::now();
    bar.close = 2500.0;
    
    EXPECT_NO_THROW(manager_->distribute_bar_data(bar));
}

// Test strategy registry
TEST(StrategyRegistryTest, RegistrationAndCreation) {
    auto& registry = StrategyRegistry::instance();
    
    // Register test strategy
    registry.register_strategy<TestStrategy>();
    
    // Check available strategies
    auto available = registry.get_available_strategies();
    // Note: This would require TestStrategy to have a static strategy_name() method
    
    // Create strategy
    StrategyConfig config;
    config.id = "test";
    config.name = "Test";
    
    // This would work if TestStrategy had proper factory support
    // auto strategy = registry.create_strategy("TestStrategy", config);
    // EXPECT_NE(strategy, nullptr);
}
