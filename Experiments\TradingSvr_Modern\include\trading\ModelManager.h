/**
 * @file ModelManager.h
 * @brief Modern model management and prediction system
 * <AUTHOR> Team
 * @date 2024
 */

#pragma once

#include "Types.h"
#include <variant>
#include <future>
#include <functional>

namespace RoboQuant::Trading {

/**
 * @brief Model types supported by the system
 */
enum class ModelType : uint8_t {
    LightGBM,
    PyTorch,
    ONNX,
    TensorFlow,
    Custom
};

/**
 * @brief Model configuration
 */
struct ModelConfig {
    std::string id;
    std::string name;
    std::string description;
    ModelType type;
    std::string model_path;
    std::string config_path;
    
    // Model parameters
    std::unordered_map<std::string, std::variant<int, double, std::string, bool>> parameters;
    
    // Feature configuration
    std::vector<std::string> feature_names;
    std::vector<std::string> categorical_features;
    size_t sequence_length{0};
    size_t prediction_length{1};
    
    // Normalization
    bool normalize_features{true};
    std::string normalization_method{"zscore"};
    
    // Performance settings
    bool use_gpu{false};
    int num_threads{1};
    
    template<typename T>
    [[nodiscard]] std::optional<T> get_parameter(const std::string& key) const {
        auto it = parameters.find(key);
        if (it != parameters.end()) {
            if (auto* value = std::get_if<T>(&it->second)) {
                return *value;
            }
        }
        return std::nullopt;
    }
};

/**
 * @brief Model prediction result
 */
struct PredictionResult {
    std::vector<double> predictions;
    std::vector<double> probabilities;
    double confidence{0.0};
    std::string model_id;
    TimePoint timestamp;
    
    [[nodiscard]] bool is_valid() const noexcept {
        return !predictions.empty() && confidence > 0.0;
    }
    
    [[nodiscard]] double get_prediction(size_t index = 0) const {
        return index < predictions.size() ? predictions[index] : 0.0;
    }
    
    [[nodiscard]] double get_probability(size_t index = 0) const {
        return index < probabilities.size() ? probabilities[index] : 0.0;
    }
};

/**
 * @brief Feature data container
 */
class FeatureData {
public:
    FeatureData() = default;
    explicit FeatureData(std::vector<std::string> feature_names);
    
    // Data management
    void add_sample(const std::vector<double>& features);
    void add_sample(const std::unordered_map<std::string, double>& features);
    void clear();
    
    // Data access
    [[nodiscard]] const std::vector<std::vector<double>>& get_data() const noexcept { return data_; }
    [[nodiscard]] const std::vector<std::string>& get_feature_names() const noexcept { return feature_names_; }
    [[nodiscard]] size_t sample_count() const noexcept { return data_.size(); }
    [[nodiscard]] size_t feature_count() const noexcept { return feature_names_.size(); }
    
    // Statistics
    [[nodiscard]] std::vector<double> get_means() const;
    [[nodiscard]] std::vector<double> get_stds() const;
    [[nodiscard]] std::vector<double> get_mins() const;
    [[nodiscard]] std::vector<double> get_maxs() const;
    
    // Normalization
    void normalize_zscore();
    void normalize_minmax();
    void normalize_robust();
    
    // Serialization
    [[nodiscard]] std::string to_json() const;
    static std::optional<FeatureData> from_json(const std::string& json);

private:
    std::vector<std::string> feature_names_;
    std::vector<std::vector<double>> data_;
};

/**
 * @brief Abstract model interface
 */
class Model {
public:
    explicit Model(ModelConfig config) : config_(std::move(config)) {}
    virtual ~Model() = default;

    // Non-copyable, movable
    Model(const Model&) = delete;
    Model& operator=(const Model&) = delete;
    Model(Model&&) = default;
    Model& operator=(Model&&) = default;

    // Model lifecycle
    virtual std::future<bool> load() = 0;
    virtual std::future<bool> unload() = 0;
    virtual bool is_loaded() const noexcept = 0;

    // Prediction
    virtual std::future<PredictionResult> predict(const FeatureData& features) = 0;
    virtual std::future<std::vector<PredictionResult>> batch_predict(
        const std::vector<FeatureData>& batch_features) = 0;

    // Configuration
    [[nodiscard]] const ModelConfig& get_config() const noexcept { return config_; }
    [[nodiscard]] const std::string& get_id() const noexcept { return config_.id; }
    [[nodiscard]] ModelType get_type() const noexcept { return config_.type; }

    // Performance metrics
    virtual std::future<std::unordered_map<std::string, double>> get_performance_metrics() = 0;

protected:
    ModelConfig config_;
};

/**
 * @brief LightGBM model implementation
 */
class LightGBMModel : public Model {
public:
    explicit LightGBMModel(ModelConfig config);
    ~LightGBMModel() override;

    std::future<bool> load() override;
    std::future<bool> unload() override;
    bool is_loaded() const noexcept override;

    std::future<PredictionResult> predict(const FeatureData& features) override;
    std::future<std::vector<PredictionResult>> batch_predict(
        const std::vector<FeatureData>& batch_features) override;

    std::future<std::unordered_map<std::string, double>> get_performance_metrics() override;

private:
    class Impl;
    std::unique_ptr<Impl> pimpl_;
};

/**
 * @brief PyTorch model implementation
 */
class PyTorchModel : public Model {
public:
    explicit PyTorchModel(ModelConfig config);
    ~PyTorchModel() override;

    std::future<bool> load() override;
    std::future<bool> unload() override;
    bool is_loaded() const noexcept override;

    std::future<PredictionResult> predict(const FeatureData& features) override;
    std::future<std::vector<PredictionResult>> batch_predict(
        const std::vector<FeatureData>& batch_features) override;

    std::future<std::unordered_map<std::string, double>> get_performance_metrics() override;

private:
    class Impl;
    std::unique_ptr<Impl> pimpl_;
};

/**
 * @brief Model factory for creating models
 */
class ModelFactory {
public:
    static UniquePtr<Model> create_model(const ModelConfig& config);
    static std::vector<ModelType> get_supported_types();
    static bool is_type_supported(ModelType type);
};

/**
 * @brief Model manager for centralized model handling
 */
class ModelManager {
public:
    ModelManager() = default;
    ~ModelManager() = default;

    // Non-copyable, movable
    ModelManager(const ModelManager&) = delete;
    ModelManager& operator=(const ModelManager&) = delete;
    ModelManager(ModelManager&&) = default;
    ModelManager& operator=(ModelManager&&) = default;

    // Model management
    std::future<bool> load_model(const ModelConfig& config);
    std::future<bool> unload_model(const std::string& model_id);
    std::future<bool> reload_model(const std::string& model_id);

    [[nodiscard]] Model* get_model(const std::string& model_id) const;
    [[nodiscard]] std::vector<std::string> get_loaded_models() const;
    [[nodiscard]] bool is_model_loaded(const std::string& model_id) const;

    // Prediction
    std::future<PredictionResult> predict(const std::string& model_id, const FeatureData& features);
    std::future<std::vector<PredictionResult>> batch_predict(
        const std::string& model_id, const std::vector<FeatureData>& batch_features);

    // Batch operations
    std::future<void> load_all_models(const std::vector<ModelConfig>& configs);
    std::future<void> unload_all_models();

    // Performance monitoring
    std::future<std::unordered_map<std::string, double>> get_model_performance(const std::string& model_id);
    [[nodiscard]] std::unordered_map<std::string, size_t> get_prediction_counts() const;

    // Configuration
    void set_max_concurrent_predictions(size_t max_concurrent) { max_concurrent_predictions_ = max_concurrent; }
    void set_prediction_timeout(Duration timeout) { prediction_timeout_ = timeout; }

    // Event callbacks
    using ModelEventCallback = std::function<void(const std::string& model_id, const std::string& event)>;
    void set_model_event_callback(ModelEventCallback callback);

private:
    mutable std::shared_mutex models_mutex_;
    std::unordered_map<std::string, UniquePtr<Model>> models_;

    // Performance tracking
    mutable std::mutex stats_mutex_;
    std::unordered_map<std::string, size_t> prediction_counts_;
    std::unordered_map<std::string, Duration> prediction_times_;

    // Configuration
    size_t max_concurrent_predictions_{10};
    Duration prediction_timeout_{std::chrono::seconds(30)};

    // Callbacks
    std::mutex callback_mutex_;
    ModelEventCallback model_event_callback_;
};

} // namespace RoboQuant::Trading
