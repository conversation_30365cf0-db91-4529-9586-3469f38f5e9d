/**
 * @file TradingServer.h
 * @brief Main trading server orchestrating all components
 * <AUTHOR> Team
 * @date 2024
 */

#pragma once

#include "Types.h"
#include "Strategy.h"
#include "Portfolio.h"
#include "OrderManager.h"
#include "DataProvider.h"
#include "ModelManager.h"
#include "ExpressionEngine.h"
#include "NetworkManager.h"
#include "Events.h"
#include <memory>
#include <future>

namespace RoboQuant::Trading {

/**
 * @brief Trading server configuration
 */
struct TradingServerConfig {
    // Server identification
    std::string server_id{"trading_server_001"};
    std::string server_name{"RoboQuant Trading Server"};
    std::string version{"1.0.0"};
    
    // Network configuration
    uint16_t server_port{8080};
    size_t network_threads{4};
    bool enable_web_interface{true};
    bool enable_api_server{true};
    
    // Data configuration
    std::string data_provider_type{"composite"};
    std::vector<std::string> data_sources;
    Duration data_cache_ttl{std::chrono::minutes(5)};
    
    // Model configuration
    std::string model_directory{"./models"};
    size_t max_concurrent_predictions{10};
    Duration prediction_timeout{std::chrono::seconds(30)};
    
    // Strategy configuration
    std::string strategy_config_file{"./config/strategies.json"};
    bool auto_start_strategies{true};
    Duration strategy_heartbeat_interval{std::chrono::seconds(30)};
    
    // Risk management
    RiskLimits global_risk_limits;
    bool enable_risk_monitoring{true};
    Duration risk_check_interval{std::chrono::seconds(10)};
    
    // Logging and monitoring
    std::string log_level{"info"};
    std::string log_directory{"./logs"};
    bool enable_performance_monitoring{true};
    Duration performance_report_interval{std::chrono::minutes(5)};
    
    // Persistence
    std::string data_directory{"./data"};
    bool enable_auto_save{true};
    Duration auto_save_interval{std::chrono::minutes(10)};
};

/**
 * @brief Trading server status
 */
enum class TradingServerStatus : uint8_t {
    Stopped,
    Starting,
    Running,
    Stopping,
    Error
};

/**
 * @brief Main trading server class
 */
class TradingServer {
public:
    explicit TradingServer(TradingServerConfig config);
    ~TradingServer();

    // Non-copyable, movable
    TradingServer(const TradingServer&) = delete;
    TradingServer& operator=(const TradingServer&) = delete;
    TradingServer(TradingServer&&) = default;
    TradingServer& operator=(TradingServer&&) = default;

    // Server lifecycle
    std::future<bool> initialize();
    std::future<void> start();
    std::future<void> stop();
    void shutdown();

    // Status and health
    [[nodiscard]] TradingServerStatus get_status() const noexcept;
    [[nodiscard]] bool is_running() const noexcept;
    [[nodiscard]] std::unordered_map<std::string, std::string> get_health_status() const;

    // Component access
    [[nodiscard]] StrategyManager& get_strategy_manager() { return *strategy_manager_; }
    [[nodiscard]] PortfolioManager& get_portfolio_manager() { return *portfolio_manager_; }
    [[nodiscard]] OrderManager& get_order_manager() { return *order_manager_; }
    [[nodiscard]] ModelManager& get_model_manager() { return *model_manager_; }
    [[nodiscard]] ExpressionManager& get_expression_manager() { return *expression_manager_; }
    [[nodiscard]] NetworkManager& get_network_manager() { return *network_manager_; }
    [[nodiscard]] EventBus& get_event_bus() { return *event_bus_; }

    // Configuration
    [[nodiscard]] const TradingServerConfig& get_config() const noexcept { return config_; }
    void update_config(const TradingServerConfig& new_config);

    // Strategy management
    std::future<bool> load_strategy(const StrategyConfig& config);
    std::future<bool> unload_strategy(const StrategyId& strategy_id);
    std::future<void> start_all_strategies();
    std::future<void> stop_all_strategies();

    // Portfolio management
    std::future<bool> create_portfolio(const PortfolioConfig& config);
    std::future<bool> remove_portfolio(const PortfolioId& portfolio_id);
    [[nodiscard]] std::vector<PortfolioId> get_portfolio_ids() const;

    // Model management
    std::future<bool> load_model(const ModelConfig& config);
    std::future<bool> unload_model(const std::string& model_id);
    [[nodiscard]] std::vector<std::string> get_loaded_models() const;

    // Data provider management
    void set_data_provider(std::unique_ptr<DataProvider> provider);
    void set_factor_data_provider(std::unique_ptr<FactorDataProvider> provider);
    void set_fundamental_data_provider(std::unique_ptr<FundamentalDataProvider> provider);

    // Risk management
    void set_global_risk_limits(const RiskLimits& limits);
    [[nodiscard]] const RiskLimits& get_global_risk_limits() const;
    [[nodiscard]] bool check_global_risk(const OrderRequest& request) const;

    // Performance monitoring
    [[nodiscard]] PerformanceMetrics get_overall_performance() const;
    [[nodiscard]] std::unordered_map<StrategyId, PerformanceMetrics> get_strategy_performance() const;
    [[nodiscard]] std::unordered_map<PortfolioId, PerformanceMetrics> get_portfolio_performance() const;

    // Event handling
    using ServerEventCallback = std::function<void(const std::string& event_type, const std::string& data)>;
    void set_server_event_callback(ServerEventCallback callback);

    // API endpoints (for external access)
    std::future<std::string> handle_api_request(const std::string& endpoint, const std::string& method, 
                                               const std::string& body);

    // Persistence
    std::future<bool> save_state();
    std::future<bool> load_state();
    void enable_auto_save(bool enable, Duration interval = std::chrono::minutes(10));

private:
    // Initialization helpers
    bool initialize_components();
    bool initialize_data_providers();
    bool initialize_strategies();
    bool initialize_portfolios();
    bool initialize_models();
    bool setup_event_handlers();
    bool setup_timers();

    // Event handlers
    void on_market_data(const QuoteData& quote);
    void on_bar_data(const BarData& bar);
    void on_order_fill(const OrderFill& fill);
    void on_order_update(const Order& order);
    void on_strategy_event(const StrategyId& strategy_id, const std::string& event);
    void on_portfolio_event(const PortfolioId& portfolio_id, const std::string& event);

    // Timer handlers
    void on_heartbeat_timer();
    void on_risk_check_timer();
    void on_performance_report_timer();
    void on_auto_save_timer();

    // API handlers
    std::string handle_status_request();
    std::string handle_strategies_request(const std::string& method, const std::string& body);
    std::string handle_portfolios_request(const std::string& method, const std::string& body);
    std::string handle_orders_request(const std::string& method, const std::string& body);
    std::string handle_models_request(const std::string& method, const std::string& body);

    // Utility methods
    void set_status(TradingServerStatus status);
    void log_info(const std::string& message) const;
    void log_warning(const std::string& message) const;
    void log_error(const std::string& message) const;
    void notify_server_event(const std::string& event_type, const std::string& data);

    // Configuration and state
    TradingServerConfig config_;
    std::atomic<TradingServerStatus> status_{TradingServerStatus::Stopped};

    // Core components
    std::unique_ptr<StrategyManager> strategy_manager_;
    std::unique_ptr<PortfolioManager> portfolio_manager_;
    std::unique_ptr<OrderManager> order_manager_;
    std::unique_ptr<ModelManager> model_manager_;
    std::unique_ptr<ExpressionManager> expression_manager_;
    std::unique_ptr<NetworkManager> network_manager_;
    std::unique_ptr<EventBus> event_bus_;

    // Data providers
    std::unique_ptr<CompositeDataProvider> composite_data_provider_;

    // Risk management
    mutable std::shared_mutex global_risk_mutex_;
    RiskLimits global_risk_limits_;

    // Performance tracking
    mutable std::mutex performance_mutex_;
    PerformanceMetrics overall_performance_;
    TimePoint start_time_;

    // Timers
    std::vector<std::string> active_timers_;

    // Auto-save
    std::atomic<bool> auto_save_enabled_{false};
    Duration auto_save_interval_{std::chrono::minutes(10)};

    // Callbacks
    std::mutex callback_mutex_;
    ServerEventCallback server_event_callback_;
};

/**
 * @brief Trading server builder for fluent configuration
 */
class TradingServerBuilder {
public:
    TradingServerBuilder() = default;

    TradingServerBuilder& with_config(const TradingServerConfig& config);
    TradingServerBuilder& with_server_port(uint16_t port);
    TradingServerBuilder& with_data_provider(std::unique_ptr<DataProvider> provider);
    TradingServerBuilder& with_strategy_config(const std::string& config_file);
    TradingServerBuilder& with_model_directory(const std::string& directory);
    TradingServerBuilder& with_risk_limits(const RiskLimits& limits);
    TradingServerBuilder& with_log_level(const std::string& level);

    [[nodiscard]] std::unique_ptr<TradingServer> build();

private:
    TradingServerConfig config_;
    std::unique_ptr<DataProvider> data_provider_;
};

} // namespace RoboQuant::Trading
