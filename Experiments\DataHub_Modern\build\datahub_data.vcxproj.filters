﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\src\data\SqliteRepository.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\src\data\HDF5Repository.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\src\data\LevelDBRepository.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\src\data\RepositoryFactory.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\include\data\IDataRepository_Simple.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\include\data\SqliteRepository.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\include\data\HDF5Repository.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\include\data\LevelDBRepository.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\include\data\RepositoryFactory.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Natvis Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\nlohmann_json-src\nlohmann_json.natvis" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{B51AFE03-D7FA-3F7D-BDDF-881A2EF2E39B}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{ED3CB8E5-FD7A-3664-A7D3-A35456B4DB16}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
