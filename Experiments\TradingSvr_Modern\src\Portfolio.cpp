/**
 * @file Portfolio.cpp
 * @brief Implementation of Portfolio class
 * <AUTHOR> Team
 * @date 2024
 */

#include "trading/Portfolio.h"
#include <algorithm>
#include <numeric>
#include <sstream>
#include <iomanip>

namespace RoboQuant::Trading {

Portfolio::Portfolio(PortfolioConfig config) 
    : config_(std::move(config)), cash_(config_.initial_capital), risk_limits_(config_.risk_limits) {}

const PortfolioConfig& Portfolio::get_config() const noexcept {
    return config_;
}

PortfolioId Portfolio::get_id() const noexcept {
    return config_.id;
}

const std::string& Portfolio::get_name() const noexcept {
    return config_.name;
}

Amount Portfolio::get_cash() const {
    std::shared_lock lock(cash_mutex_);
    return cash_;
}

void Portfolio::add_cash(Amount amount) {
    std::unique_lock lock(cash_mutex_);
    Amount old_cash = cash_;
    cash_ += amount;
    lock.unlock();
    
    notify_cash_update(cash_, amount);
}

void Portfolio::withdraw_cash(Amount amount) {
    std::unique_lock lock(cash_mutex_);
    Amount old_cash = cash_;
    cash_ -= amount;
    lock.unlock();
    
    notify_cash_update(cash_, -amount);
}

bool Portfolio::has_sufficient_cash(Amount required) const {
    std::shared_lock lock(cash_mutex_);
    return cash_ >= required;
}

std::optional<Position> Portfolio::get_position(const AssetId& asset_id) const {
    std::shared_lock lock(positions_mutex_);
    auto it = positions_.find(asset_id);
    return it != positions_.end() ? std::make_optional(it->second) : std::nullopt;
}

std::vector<Position> Portfolio::get_all_positions() const {
    std::shared_lock lock(positions_mutex_);
    std::vector<Position> result;
    result.reserve(positions_.size());
    
    for (const auto& [asset_id, position] : positions_) {
        if (!position.is_flat()) {
            result.push_back(position);
        }
    }
    
    return result;
}

bool Portfolio::has_position(const AssetId& asset_id) const {
    std::shared_lock lock(positions_mutex_);
    auto it = positions_.find(asset_id);
    return it != positions_.end() && !it->second.is_flat();
}

void Portfolio::update_position(const AssetId& asset_id, Quantity quantity, Price price, Amount commission) {
    std::unique_lock positions_lock(positions_mutex_);
    
    // Get or create position
    auto& position = positions_[asset_id];
    if (position.asset_id().empty()) {
        position = Position(asset_id);
    }
    
    // Calculate cash impact
    Amount cash_impact = static_cast<Amount>(quantity) * price + commission;
    
    // Update cash
    positions_lock.unlock();
    std::unique_lock cash_lock(cash_mutex_);
    
    if (quantity > 0) { // Buying
        cash_ -= cash_impact;
    } else { // Selling
        cash_ += cash_impact;
    }
    
    cash_lock.unlock();
    positions_lock.lock();
    
    // Update position
    Side side = (quantity > 0) ? Side::Buy : Side::Sell;
    position.add_trade(side, std::abs(quantity), price, commission);
    
    // Update realized P&L
    realized_pnl_ += position.realized_pnl();
    
    positions_lock.unlock();
    
    notify_position_update(asset_id, position);
    notify_cash_update(cash_, -cash_impact);
}

void Portfolio::close_position(const AssetId& asset_id, Price price, Amount commission) {
    std::unique_lock lock(positions_mutex_);
    
    auto it = positions_.find(asset_id);
    if (it == positions_.end() || it->second.is_flat()) {
        return;
    }
    
    auto& position = it->second;
    Amount pnl = position.close_position(price, commission);
    
    // Update cash with proceeds
    Amount proceeds = static_cast<Amount>(std::abs(position.quantity())) * price - commission;
    lock.unlock();
    
    std::unique_lock cash_lock(cash_mutex_);
    cash_ += proceeds;
    realized_pnl_ += pnl;
    cash_lock.unlock();
    
    notify_position_update(asset_id, position);
    notify_cash_update(cash_, proceeds);
}

void Portfolio::close_all_positions(const std::function<Price(const AssetId&)>& price_provider) {
    std::shared_lock lock(positions_mutex_);
    std::vector<AssetId> assets_to_close;
    
    for (const auto& [asset_id, position] : positions_) {
        if (!position.is_flat()) {
            assets_to_close.push_back(asset_id);
        }
    }
    lock.unlock();
    
    for (const auto& asset_id : assets_to_close) {
        Price current_price = price_provider(asset_id);
        close_position(asset_id, current_price);
    }
}

Amount Portfolio::get_total_value(const std::function<Price(const AssetId&)>& price_provider) const {
    Amount cash = get_cash();
    Amount position_value = 0.0;
    
    std::shared_lock lock(positions_mutex_);
    for (const auto& [asset_id, position] : positions_) {
        if (!position.is_flat()) {
            Price current_price = price_provider(asset_id);
            position_value += position.market_value(current_price);
        }
    }
    
    return cash + position_value;
}

Amount Portfolio::get_unrealized_pnl(const std::function<Price(const AssetId&)>& price_provider) const {
    Amount unrealized_pnl = 0.0;
    
    std::shared_lock lock(positions_mutex_);
    for (const auto& [asset_id, position] : positions_) {
        if (!position.is_flat()) {
            Price current_price = price_provider(asset_id);
            unrealized_pnl += position.unrealized_pnl(current_price);
        }
    }
    
    return unrealized_pnl;
}

Amount Portfolio::get_realized_pnl() const {
    std::shared_lock lock(cash_mutex_);
    return realized_pnl_;
}

PerformanceMetrics Portfolio::calculate_performance(
    const std::function<Price(const AssetId&)>& price_provider) const {
    
    PerformanceMetrics metrics;
    metrics.last_updated = std::chrono::system_clock::now();
    
    Amount total_value = get_total_value(price_provider);
    metrics.unrealized_pnl = get_unrealized_pnl(price_provider);
    metrics.realized_pnl = get_realized_pnl();
    metrics.total_return = total_value - config_.initial_capital;
    
    // Calculate trade count
    std::shared_lock lock(positions_mutex_);
    for (const auto& [asset_id, position] : positions_) {
        metrics.total_trades += static_cast<uint32_t>(position.trade_count());
    }
    
    // Simple Sharpe ratio calculation (would need historical returns for proper calculation)
    if (config_.initial_capital > 0) {
        double return_rate = metrics.total_return / config_.initial_capital;
        metrics.sharpe_ratio = return_rate / 0.15; // Assume 15% volatility
    }
    
    return metrics;
}

const RiskLimits& Portfolio::get_risk_limits() const {
    std::shared_lock lock(risk_limits_mutex_);
    return risk_limits_;
}

void Portfolio::set_risk_limits(const RiskLimits& limits) {
    std::unique_lock lock(risk_limits_mutex_);
    risk_limits_ = limits;
}

bool Portfolio::check_risk_limits(const OrderRequest& request, Price current_price) const {
    std::shared_lock lock(risk_limits_mutex_);
    
    // Check position value limit
    Amount position_value = static_cast<Amount>(request.quantity) * current_price;
    if (position_value > risk_limits_.max_position_value) {
        return false;
    }
    
    // Check leverage limit
    Amount total_value = get_total_value([current_price](const AssetId&) { return current_price; });
    if (total_value > 0) {
        double leverage = position_value / total_value;
        if (leverage > risk_limits_.max_leverage) {
            return false;
        }
    }
    
    return true;
}

PortfolioSnapshot Portfolio::create_snapshot(
    const std::function<Price(const AssetId&)>& price_provider) const {
    
    PortfolioSnapshot snapshot;
    snapshot.timestamp = std::chrono::system_clock::now();
    snapshot.cash = get_cash();
    snapshot.unrealized_pnl = get_unrealized_pnl(price_provider);
    snapshot.realized_pnl = get_realized_pnl();
    snapshot.total_value = get_total_value(price_provider);
    
    std::shared_lock lock(positions_mutex_);
    snapshot.positions = positions_;
    
    return snapshot;
}

void Portfolio::add_snapshot(const PortfolioSnapshot& snapshot) {
    std::unique_lock lock(snapshots_mutex_);
    snapshots_.push_back(snapshot);
    
    // Keep only last 1000 snapshots to prevent memory growth
    if (snapshots_.size() > 1000) {
        snapshots_.erase(snapshots_.begin(), snapshots_.begin() + 100);
    }
}

std::vector<PortfolioSnapshot> Portfolio::get_snapshots(
    std::optional<TimePoint> start_time, std::optional<TimePoint> end_time) const {
    
    std::shared_lock lock(snapshots_mutex_);
    
    if (!start_time && !end_time) {
        return snapshots_;
    }
    
    std::vector<PortfolioSnapshot> result;
    std::copy_if(snapshots_.begin(), snapshots_.end(), std::back_inserter(result),
                 [start_time, end_time](const PortfolioSnapshot& snapshot) {
                     bool after_start = !start_time || snapshot.timestamp >= *start_time;
                     bool before_end = !end_time || snapshot.timestamp <= *end_time;
                     return after_start && before_end;
                 });
    
    return result;
}

void Portfolio::set_position_update_callback(PositionUpdateCallback callback) {
    std::lock_guard lock(callbacks_mutex_);
    position_callback_ = std::move(callback);
}

void Portfolio::set_cash_update_callback(CashUpdateCallback callback) {
    std::lock_guard lock(callbacks_mutex_);
    cash_callback_ = std::move(callback);
}

bool Portfolio::is_enabled() const noexcept {
    return enabled_.load();
}

void Portfolio::enable() noexcept {
    enabled_.store(true);
}

void Portfolio::disable() noexcept {
    enabled_.store(false);
}

void Portfolio::notify_position_update(const AssetId& asset_id, const Position& position) {
    std::lock_guard lock(callbacks_mutex_);
    if (position_callback_) {
        position_callback_(asset_id, position);
    }
}

void Portfolio::notify_cash_update(Amount new_cash, Amount change) {
    std::lock_guard lock(callbacks_mutex_);
    if (cash_callback_) {
        cash_callback_(new_cash, change);
    }
}

std::string Portfolio::to_json() const {
    std::ostringstream oss;
    oss << std::fixed << std::setprecision(6);
    oss << "{"
        << "\"id\":\"" << config_.id << "\","
        << "\"name\":\"" << config_.name << "\","
        << "\"cash\":" << get_cash() << ","
        << "\"realized_pnl\":" << get_realized_pnl() << ","
        << "\"enabled\":" << (is_enabled() ? "true" : "false") << ","
        << "\"position_count\":" << get_all_positions().size()
        << "}";
    return oss.str();
}

// Portfolio Manager Implementation
bool PortfolioManager::add_portfolio(UniquePtr<Portfolio> portfolio) {
    if (!portfolio) return false;
    
    std::unique_lock lock(portfolios_mutex_);
    auto id = portfolio->get_id();
    
    if (portfolios_.find(id) != portfolios_.end()) {
        return false; // Portfolio already exists
    }
    
    portfolios_[id] = std::move(portfolio);
    return true;
}

bool PortfolioManager::remove_portfolio(const PortfolioId& id) {
    std::unique_lock lock(portfolios_mutex_);
    auto it = portfolios_.find(id);
    
    if (it == portfolios_.end()) {
        return false;
    }
    
    portfolios_.erase(it);
    return true;
}

Portfolio* PortfolioManager::get_portfolio(const PortfolioId& id) const {
    std::shared_lock lock(portfolios_mutex_);
    auto it = portfolios_.find(id);
    return it != portfolios_.end() ? it->second.get() : nullptr;
}

std::vector<Portfolio*> PortfolioManager::get_all_portfolios() const {
    std::shared_lock lock(portfolios_mutex_);
    std::vector<Portfolio*> result;
    result.reserve(portfolios_.size());
    
    for (const auto& [id, portfolio] : portfolios_) {
        result.push_back(portfolio.get());
    }
    
    return result;
}

std::vector<PortfolioId> PortfolioManager::get_portfolio_ids() const {
    std::shared_lock lock(portfolios_mutex_);
    std::vector<PortfolioId> result;
    result.reserve(portfolios_.size());
    
    for (const auto& [id, portfolio] : portfolios_) {
        result.push_back(id);
    }
    
    return result;
}

bool PortfolioManager::has_portfolio(const PortfolioId& id) const {
    std::shared_lock lock(portfolios_mutex_);
    return portfolios_.find(id) != portfolios_.end();
}

size_t PortfolioManager::portfolio_count() const {
    std::shared_lock lock(portfolios_mutex_);
    return portfolios_.size();
}

void PortfolioManager::update_all_snapshots(const std::function<Price(const AssetId&)>& price_provider) {
    std::shared_lock lock(portfolios_mutex_);
    
    for (const auto& [id, portfolio] : portfolios_) {
        auto snapshot = portfolio->create_snapshot(price_provider);
        portfolio->add_snapshot(snapshot);
    }
}

Amount PortfolioManager::get_total_value(const std::function<Price(const AssetId&)>& price_provider) const {
    std::shared_lock lock(portfolios_mutex_);
    Amount total = 0.0;
    
    for (const auto& [id, portfolio] : portfolios_) {
        total += portfolio->get_total_value(price_provider);
    }
    
    return total;
}

void PortfolioManager::set_global_risk_limits(const RiskLimits& limits) {
    std::unique_lock lock(global_risk_mutex_);
    global_risk_limits_ = limits;
}

const RiskLimits& PortfolioManager::get_global_risk_limits() const {
    std::shared_lock lock(global_risk_mutex_);
    return global_risk_limits_;
}

bool PortfolioManager::check_global_risk_limits(const OrderRequest& request, Price current_price) const {
    std::shared_lock lock(global_risk_mutex_);
    
    Amount position_value = static_cast<Amount>(request.quantity) * current_price;
    return position_value <= global_risk_limits_.max_position_value;
}

} // namespace RoboQuant::Trading
