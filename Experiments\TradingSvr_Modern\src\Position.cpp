/**
 * @file Position.cpp
 * @brief Implementation of Position class
 * <AUTHOR> Team
 * @date 2024
 */

#include "trading/Position.h"
#include <algorithm>
#include <numeric>
#include <cmath>
#include <sstream>
#include <iomanip>

namespace RoboQuant::Trading {

Position::Position(AssetId asset_id) : asset_id_(std::move(asset_id)) {}

Amount Position::unrealized_pnl(Price current_price) const noexcept {
    if (quantity_ == 0) return 0.0;
    
    Amount current_value = static_cast<Amount>(std::abs(quantity_)) * current_price;
    Amount cost_basis = static_cast<Amount>(std::abs(quantity_)) * average_price_;
    
    if (quantity_ > 0) {
        // Long position: profit when current price > average price
        return current_value - cost_basis;
    } else {
        // Short position: profit when current price < average price
        return cost_basis - current_value;
    }
}

void Position::add_trade(const Trade& trade) {
    trades_.push_back(trade);
    
    if (trade.side == Side::Buy) {
        if (quantity_ >= 0) {
            // Adding to long position or opening long
            total_cost_ += trade.gross_value();
            quantity_ += trade.quantity;
        } else {
            // Covering short position
            Quantity covered = std::min(static_cast<Quantity>(std::abs(quantity_)), trade.quantity);
            calculate_realized_pnl(trade, covered);
            quantity_ += trade.quantity;
            
            if (quantity_ > 0) {
                // Remaining quantity becomes new long position
                total_cost_ = static_cast<Amount>(quantity_) * trade.price;
            }
        }
    } else { // Side::Sell
        if (quantity_ <= 0) {
            // Adding to short position or opening short
            total_cost_ += trade.gross_value();
            quantity_ -= trade.quantity;
        } else {
            // Closing long position
            Quantity closed = std::min(quantity_, trade.quantity);
            calculate_realized_pnl(trade, closed);
            quantity_ -= trade.quantity;
            
            if (quantity_ < 0) {
                // Remaining quantity becomes new short position
                total_cost_ = static_cast<Amount>(std::abs(quantity_)) * trade.price;
            }
        }
    }
    
    total_commission_ += trade.commission;
    update_average_price();
}

void Position::add_trade(Side side, Quantity quantity, Price price, Amount commission,
                        const std::string& order_id, TimePoint timestamp) {
    Trade trade;
    trade.timestamp = timestamp;
    trade.side = side;
    trade.quantity = quantity;
    trade.price = price;
    trade.commission = commission;
    trade.order_id = order_id;
    
    add_trade(trade);
}

Amount Position::close_position(Price price, Amount commission, 
                               const std::string& order_id, TimePoint timestamp) {
    if (quantity_ == 0) return 0.0;
    
    Side close_side = (quantity_ > 0) ? Side::Sell : Side::Buy;
    Quantity close_quantity = std::abs(quantity_);
    
    Trade closing_trade;
    closing_trade.timestamp = timestamp;
    closing_trade.side = close_side;
    closing_trade.quantity = close_quantity;
    closing_trade.price = price;
    closing_trade.commission = commission;
    closing_trade.order_id = order_id;
    
    Amount pnl_before = realized_pnl_;
    add_trade(closing_trade);
    
    return realized_pnl_ - pnl_before;
}

Amount Position::reduce_position(Quantity quantity_to_reduce, Price price, Amount commission,
                                const std::string& order_id, TimePoint timestamp) {
    if (quantity_ == 0 || quantity_to_reduce <= 0) return 0.0;
    
    Quantity actual_reduction = std::min(quantity_to_reduce, static_cast<Quantity>(std::abs(quantity_)));
    Side reduce_side = (quantity_ > 0) ? Side::Sell : Side::Buy;
    
    Trade reducing_trade;
    reducing_trade.timestamp = timestamp;
    reducing_trade.side = reduce_side;
    reducing_trade.quantity = actual_reduction;
    reducing_trade.price = price;
    reducing_trade.commission = commission;
    reducing_trade.order_id = order_id;
    
    Amount pnl_before = realized_pnl_;
    add_trade(reducing_trade);
    
    return realized_pnl_ - pnl_before;
}

std::vector<Trade> Position::get_trades_in_range(TimePoint start, TimePoint end) const {
    std::vector<Trade> result;
    std::copy_if(trades_.begin(), trades_.end(), std::back_inserter(result),
                 [start, end](const Trade& trade) {
                     return trade.timestamp >= start && trade.timestamp <= end;
                 });
    return result;
}

TimePoint Position::first_trade_time() const {
    if (trades_.empty()) return TimePoint{};
    return trades_.front().timestamp;
}

TimePoint Position::last_trade_time() const {
    if (trades_.empty()) return TimePoint{};
    return trades_.back().timestamp;
}

Duration Position::holding_period() const {
    if (trades_.empty()) return Duration::zero();
    return std::chrono::duration_cast<Duration>(last_trade_time() - first_trade_time());
}

double Position::leverage(Price current_price, Amount account_value) const noexcept {
    if (account_value <= 0.0 || quantity_ == 0) return 0.0;
    
    Amount position_value = market_value(current_price);
    return position_value / account_value;
}

Amount Position::value_at_risk(Price current_price, double confidence_level) const noexcept {
    if (quantity_ == 0) return 0.0;
    
    // Simplified VaR calculation using normal distribution assumption
    // In practice, this would use historical price data and more sophisticated models
    Amount position_value = market_value(current_price);
    double volatility = 0.02; // Assume 2% daily volatility (should be calculated from data)
    double z_score = (confidence_level == 0.95) ? 1.645 : 
                     (confidence_level == 0.99) ? 2.326 : 1.96;
    
    return position_value * volatility * z_score;
}

void Position::update_average_price() {
    if (quantity_ == 0) {
        average_price_ = 0.0;
        total_cost_ = 0.0;
    } else {
        average_price_ = total_cost_ / static_cast<Amount>(std::abs(quantity_));
    }
}

void Position::calculate_realized_pnl(const Trade& closing_trade, Quantity closed_quantity) {
    if (closed_quantity <= 0) return;
    
    Amount cost_basis = static_cast<Amount>(closed_quantity) * average_price_;
    Amount proceeds = static_cast<Amount>(closed_quantity) * closing_trade.price;
    
    if (quantity_ > 0) {
        // Closing long position
        realized_pnl_ += proceeds - cost_basis;
        total_cost_ -= cost_basis;
    } else {
        // Covering short position
        realized_pnl_ += cost_basis - proceeds;
        total_cost_ -= cost_basis;
    }
}

bool Position::operator==(const Position& other) const noexcept {
    return asset_id_ == other.asset_id_ &&
           quantity_ == other.quantity_ &&
           std::abs(average_price_ - other.average_price_) < 1e-6 &&
           std::abs(total_cost_ - other.total_cost_) < 1e-6 &&
           std::abs(realized_pnl_ - other.realized_pnl_) < 1e-6;
}

std::string Position::to_json() const {
    std::ostringstream oss;
    oss << std::fixed << std::setprecision(6);
    oss << "{"
        << "\"asset_id\":\"" << asset_id_ << "\","
        << "\"quantity\":" << quantity_ << ","
        << "\"average_price\":" << average_price_ << ","
        << "\"total_cost\":" << total_cost_ << ","
        << "\"total_commission\":" << total_commission_ << ","
        << "\"realized_pnl\":" << realized_pnl_ << ","
        << "\"trade_count\":" << trades_.size()
        << "}";
    return oss.str();
}

// Position Analytics Implementation
double PositionAnalytics::calculate_return(const Position& position, Price current_price) {
    if (position.quantity() == 0 || position.total_cost() == 0.0) return 0.0;
    
    Amount total_pnl = position.total_pnl(current_price);
    return total_pnl / position.total_cost();
}

double PositionAnalytics::calculate_annualized_return(const Position& position, Price current_price) {
    double total_return = calculate_return(position, current_price);
    Duration holding_period = position.holding_period();
    
    if (holding_period <= Duration::zero()) return 0.0;
    
    double days = static_cast<double>(holding_period.count()) / (1000.0 * 60.0 * 60.0 * 24.0);
    if (days <= 0.0) return 0.0;
    
    return std::pow(1.0 + total_return, 365.0 / days) - 1.0;
}

double PositionAnalytics::calculate_win_rate(const Position& position) {
    const auto& trades = position.get_trades();
    if (trades.size() < 2) return 0.0;
    
    int winning_trades = 0;
    int total_round_trips = 0;
    
    // Simple win rate calculation based on profitable vs unprofitable trades
    // This is a simplified version - in practice would need more sophisticated trade matching
    for (size_t i = 1; i < trades.size(); ++i) {
        if (trades[i-1].side != trades[i].side) {
            total_round_trips++;
            if ((trades[i-1].side == Side::Buy && trades[i].price > trades[i-1].price) ||
                (trades[i-1].side == Side::Sell && trades[i].price < trades[i-1].price)) {
                winning_trades++;
            }
        }
    }
    
    return total_round_trips > 0 ? static_cast<double>(winning_trades) / total_round_trips : 0.0;
}

// Position Factory Implementation
Position PositionFactory::from_trades(const AssetId& asset_id, const std::vector<Trade>& trades) {
    Position position(asset_id);
    for (const auto& trade : trades) {
        position.add_trade(trade);
    }
    return position;
}

Position PositionFactory::from_order_fills(const AssetId& asset_id, const std::vector<OrderFill>& fills) {
    Position position(asset_id);
    for (const auto& fill : fills) {
        Trade trade;
        trade.timestamp = fill.timestamp;
        trade.side = fill.side;
        trade.quantity = fill.quantity;
        trade.price = fill.price;
        trade.commission = fill.commission;
        trade.order_id = fill.order_id;
        
        position.add_trade(trade);
    }
    return position;
}

Position PositionFactory::create_long_position(const AssetId& asset_id, Quantity quantity, Price price) {
    Position position(asset_id);
    position.add_trade(Side::Buy, quantity, price);
    return position;
}

Position PositionFactory::create_short_position(const AssetId& asset_id, Quantity quantity, Price price) {
    Position position(asset_id);
    position.add_trade(Side::Sell, quantity, price);
    return position;
}

} // namespace RoboQuant::Trading
