/**
 * @file test_portfolio.cpp
 * @brief Unit tests for Portfolio class
 * <AUTHOR> Team
 * @date 2024
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "trading/Portfolio.h"
#include "trading/Position.h"

using namespace RoboQuant::Trading;
using ::testing::_;
using ::testing::Return;
using ::testing::StrictMock;

class PortfolioTest : public ::testing::Test {
protected:
    void SetUp() override {
        config_.id = "test_portfolio";
        config_.name = "Test Portfolio";
        config_.description = "Portfolio for unit testing";
        config_.initial_capital = 100000.0;
        config_.base_currency = "USD";
        config_.enabled = true;
        
        // Set up risk limits
        config_.risk_limits.max_position_value = 50000.0;
        config_.risk_limits.max_daily_loss = 5000.0;
        config_.risk_limits.max_drawdown = 10000.0;
        config_.risk_limits.max_leverage = 2.0;
        config_.risk_limits.max_orders_per_second = 10;
        
        portfolio_ = std::make_unique<Portfolio>(config_);
    }

    PortfolioConfig config_;
    std::unique_ptr<Portfolio> portfolio_;
};

TEST_F(PortfolioTest, InitialState) {
    EXPECT_EQ(portfolio_->get_id(), "test_portfolio");
    EXPECT_EQ(portfolio_->get_name(), "Test Portfolio");
    EXPECT_EQ(portfolio_->get_cash(), 100000.0);
    EXPECT_TRUE(portfolio_->is_enabled());
    EXPECT_EQ(portfolio_->get_all_positions().size(), 0);
}

TEST_F(PortfolioTest, CashManagement) {
    // Test adding cash
    portfolio_->add_cash(10000.0);
    EXPECT_EQ(portfolio_->get_cash(), 110000.0);
    
    // Test withdrawing cash
    portfolio_->withdraw_cash(5000.0);
    EXPECT_EQ(portfolio_->get_cash(), 105000.0);
    
    // Test insufficient cash check
    EXPECT_TRUE(portfolio_->has_sufficient_cash(50000.0));
    EXPECT_FALSE(portfolio_->has_sufficient_cash(200000.0));
}

TEST_F(PortfolioTest, PositionManagement) {
    const AssetId asset_id = "AAPL";
    const Price price = 150.0;
    const Quantity quantity = 100;
    const Amount commission = 1.0;
    
    // Test position creation
    EXPECT_FALSE(portfolio_->has_position(asset_id));
    
    portfolio_->update_position(asset_id, quantity, price, commission);
    
    EXPECT_TRUE(portfolio_->has_position(asset_id));
    
    auto position = portfolio_->get_position(asset_id);
    ASSERT_TRUE(position.has_value());
    EXPECT_EQ(position->quantity(), quantity);
    EXPECT_EQ(position->average_price(), price);
    EXPECT_EQ(position->total_commission(), commission);
    
    // Test position update
    portfolio_->update_position(asset_id, 50, 155.0, 0.5);
    
    position = portfolio_->get_position(asset_id);
    ASSERT_TRUE(position.has_value());
    EXPECT_EQ(position->quantity(), 150); // 100 + 50
    EXPECT_NEAR(position->average_price(), 151.67, 0.01); // Weighted average
    EXPECT_EQ(position->total_commission(), 1.5); // 1.0 + 0.5
}

TEST_F(PortfolioTest, PositionClosing) {
    const AssetId asset_id = "GOOGL";
    const Price entry_price = 2500.0;
    const Quantity quantity = 10;
    const Price exit_price = 2600.0;
    
    // Create position
    portfolio_->update_position(asset_id, quantity, entry_price);
    EXPECT_TRUE(portfolio_->has_position(asset_id));
    
    // Close position
    portfolio_->close_position(asset_id, exit_price);
    EXPECT_FALSE(portfolio_->has_position(asset_id));
    
    // Check realized P&L
    EXPECT_EQ(portfolio_->get_realized_pnl(), 1000.0); // (2600 - 2500) * 10
}

TEST_F(PortfolioTest, PortfolioValuation) {
    const AssetId asset1 = "AAPL";
    const AssetId asset2 = "GOOGL";
    
    // Add positions
    portfolio_->update_position(asset1, 100, 150.0);
    portfolio_->update_position(asset2, 10, 2500.0);
    
    // Price provider function
    auto price_provider = [](const AssetId& asset_id) -> Price {
        if (asset_id == "AAPL") return 155.0;
        if (asset_id == "GOOGL") return 2600.0;
        return 0.0;
    };
    
    // Test total value calculation
    Amount expected_cash = 100000.0 - (100 * 150.0) - (10 * 2500.0); // Initial cash - position costs
    Amount expected_position_value = (100 * 155.0) + (10 * 2600.0); // Current position values
    Amount expected_total = expected_cash + expected_position_value;
    
    EXPECT_EQ(portfolio_->get_total_value(price_provider), expected_total);
    
    // Test unrealized P&L
    Amount expected_unrealized_pnl = (100 * (155.0 - 150.0)) + (10 * (2600.0 - 2500.0));
    EXPECT_EQ(portfolio_->get_unrealized_pnl(price_provider), expected_unrealized_pnl);
}

TEST_F(PortfolioTest, RiskLimits) {
    OrderRequest request;
    request.asset_id = "TSLA";
    request.side = Side::Buy;
    request.type = OrderType::Market;
    request.quantity = 100;
    request.portfolio_id = portfolio_->get_id();
    
    Price current_price = 800.0;
    
    // Test within risk limits
    EXPECT_TRUE(portfolio_->check_risk_limits(request, current_price));
    
    // Test exceeding position value limit
    request.quantity = 1000; // 1000 * 800 = 800,000 > 50,000 limit
    EXPECT_FALSE(portfolio_->check_risk_limits(request, current_price));
}

TEST_F(PortfolioTest, PerformanceMetrics) {
    const AssetId asset_id = "MSFT";
    
    // Create and close a profitable position
    portfolio_->update_position(asset_id, 100, 300.0);
    portfolio_->close_position(asset_id, 320.0);
    
    auto price_provider = [](const AssetId&) -> Price { return 0.0; };
    
    auto metrics = portfolio_->calculate_performance(price_provider);
    
    EXPECT_EQ(metrics.realized_pnl, 2000.0); // (320 - 300) * 100
    EXPECT_EQ(metrics.unrealized_pnl, 0.0); // No open positions
    EXPECT_EQ(metrics.total_return, 2000.0);
}

TEST_F(PortfolioTest, Snapshots) {
    auto price_provider = [](const AssetId& asset_id) -> Price {
        if (asset_id == "AAPL") return 150.0;
        return 0.0;
    };
    
    // Create initial snapshot
    auto snapshot1 = portfolio_->create_snapshot(price_provider);
    EXPECT_EQ(snapshot1.cash, 100000.0);
    EXPECT_EQ(snapshot1.total_value, 100000.0);
    
    // Add position and create another snapshot
    portfolio_->update_position("AAPL", 100, 150.0);
    auto snapshot2 = portfolio_->create_snapshot(price_provider);
    
    EXPECT_EQ(snapshot2.cash, 85000.0); // 100000 - (100 * 150)
    EXPECT_EQ(snapshot2.total_value, 100000.0); // Cash + position value
    EXPECT_EQ(snapshot2.positions.size(), 1);
    
    // Add snapshots to history
    portfolio_->add_snapshot(snapshot1);
    portfolio_->add_snapshot(snapshot2);
    
    auto snapshots = portfolio_->get_snapshots();
    EXPECT_EQ(snapshots.size(), 2);
}

TEST_F(PortfolioTest, EnableDisable) {
    EXPECT_TRUE(portfolio_->is_enabled());
    
    portfolio_->disable();
    EXPECT_FALSE(portfolio_->is_enabled());
    
    portfolio_->enable();
    EXPECT_TRUE(portfolio_->is_enabled());
}

// Mock callback for testing
class MockPortfolioCallback {
public:
    MOCK_METHOD(void, on_position_update, (const AssetId&, const Position&));
    MOCK_METHOD(void, on_cash_update, (Amount, Amount));
};

TEST_F(PortfolioTest, Callbacks) {
    StrictMock<MockPortfolioCallback> mock_callback;
    
    // Set up callbacks
    portfolio_->set_position_update_callback(
        [&mock_callback](const AssetId& asset_id, const Position& position) {
            mock_callback.on_position_update(asset_id, position);
        }
    );
    
    portfolio_->set_cash_update_callback(
        [&mock_callback](Amount new_cash, Amount change) {
            mock_callback.on_cash_update(new_cash, change);
        }
    );
    
    // Expect callbacks to be called
    EXPECT_CALL(mock_callback, on_position_update("AAPL", _)).Times(1);
    EXPECT_CALL(mock_callback, on_cash_update(110000.0, 10000.0)).Times(1);
    
    // Trigger callbacks
    portfolio_->update_position("AAPL", 100, 150.0);
    portfolio_->add_cash(10000.0);
}

// Test portfolio manager
class PortfolioManagerTest : public ::testing::Test {
protected:
    void SetUp() override {
        manager_ = std::make_unique<PortfolioManager>();
    }

    std::unique_ptr<PortfolioManager> manager_;
};

TEST_F(PortfolioManagerTest, PortfolioManagement) {
    PortfolioConfig config;
    config.id = "test_portfolio_1";
    config.name = "Test Portfolio 1";
    config.initial_capital = 100000.0;
    
    auto portfolio = std::make_unique<Portfolio>(config);
    auto* portfolio_ptr = portfolio.get();
    
    // Add portfolio
    EXPECT_TRUE(manager_->add_portfolio(std::move(portfolio)));
    EXPECT_TRUE(manager_->has_portfolio("test_portfolio_1"));
    EXPECT_EQ(manager_->portfolio_count(), 1);
    
    // Get portfolio
    auto* retrieved_portfolio = manager_->get_portfolio("test_portfolio_1");
    EXPECT_EQ(retrieved_portfolio, portfolio_ptr);
    
    // Remove portfolio
    EXPECT_TRUE(manager_->remove_portfolio("test_portfolio_1"));
    EXPECT_FALSE(manager_->has_portfolio("test_portfolio_1"));
    EXPECT_EQ(manager_->portfolio_count(), 0);
}

TEST_F(PortfolioManagerTest, GlobalRiskLimits) {
    RiskLimits global_limits;
    global_limits.max_position_value = 1000000.0;
    global_limits.max_daily_loss = 50000.0;
    
    manager_->set_global_risk_limits(global_limits);
    
    auto retrieved_limits = manager_->get_global_risk_limits();
    EXPECT_EQ(retrieved_limits.max_position_value, 1000000.0);
    EXPECT_EQ(retrieved_limits.max_daily_loss, 50000.0);
}
