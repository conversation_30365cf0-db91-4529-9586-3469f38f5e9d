#!/bin/bash

# Build script for TradingSvr_Modern on Linux/macOS

set -e  # Exit on any error

# Default values
BUILD_TYPE="Release"
CLEAN=false
TEST=false
INSTALL=false
GENERATOR="Unix Makefiles"
JOBS=$(nproc 2>/dev/null || sysctl -n hw.ncpu 2>/dev/null || echo 4)

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

function print_info() {
    echo -e "${BLUE}INFO: $1${NC}"
}

function print_success() {
    echo -e "${GREEN}SUCCESS: $1${NC}"
}

function print_warning() {
    echo -e "${YELLOW}WARNING: $1${NC}"
}

function print_error() {
    echo -e "${RED}ERROR: $1${NC}"
}

function usage() {
    echo "Usage: $0 [OPTIONS]"
    echo "Options:"
    echo "  -t, --type TYPE      Build type (Debug|Release|RelWithDebInfo|MinSizeRel) [default: Release]"
    echo "  -c, --clean          Clean build directory before building"
    echo "  -T, --test           Run tests after building"
    echo "  -i, --install        Install after building"
    echo "  -j, --jobs JOBS      Number of parallel jobs [default: auto-detected]"
    echo "  -g, --generator GEN  CMake generator [default: Unix Makefiles]"
    echo "  -h, --help           Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                           # Release build"
    echo "  $0 -t Debug                  # Debug build"
    echo "  $0 -c                        # Clean and build"
    echo "  $0 -T                        # Build and run tests"
    echo "  $0 -i                        # Build and install"
    echo "  $0 -c -T -i                  # Full build cycle"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -t|--type)
            BUILD_TYPE="$2"
            shift 2
            ;;
        -c|--clean)
            CLEAN=true
            shift
            ;;
        -T|--test)
            TEST=true
            shift
            ;;
        -i|--install)
            INSTALL=true
            shift
            ;;
        -j|--jobs)
            JOBS="$2"
            shift 2
            ;;
        -g|--generator)
            GENERATOR="$2"
            shift 2
            ;;
        -h|--help)
            usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            usage
            exit 1
            ;;
    esac
done

# Check if CMake is available
if ! command -v cmake &> /dev/null; then
    print_error "CMake is not installed or not in PATH"
    exit 1
fi

# Project root directory
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BUILD_DIR="$PROJECT_ROOT/build"

print_info "Building TradingSvr_Modern"
print_info "Build Type: $BUILD_TYPE"
print_info "Generator: $GENERATOR"
print_info "Jobs: $JOBS"
print_info "Project Root: $PROJECT_ROOT"
print_info "Build Directory: $BUILD_DIR"

# Clean build directory if requested
if [ "$CLEAN" = true ] && [ -d "$BUILD_DIR" ]; then
    print_info "Cleaning build directory..."
    rm -rf "$BUILD_DIR"
    print_success "Build directory cleaned"
fi

# Create build directory
if [ ! -d "$BUILD_DIR" ]; then
    print_info "Creating build directory..."
    mkdir -p "$BUILD_DIR"
fi

# Change to build directory
cd "$BUILD_DIR"

# Configure
print_info "Configuring project..."
cmake .. \
    -G "$GENERATOR" \
    -DCMAKE_BUILD_TYPE="$BUILD_TYPE" \
    -DCMAKE_INSTALL_PREFIX="$BUILD_DIR/install" \
    -DCMAKE_EXPORT_COMPILE_COMMANDS=ON

print_success "Configuration completed"

# Build
print_info "Building project..."
cmake --build . --config "$BUILD_TYPE" --parallel "$JOBS"
print_success "Build completed"

# Run tests if requested
if [ "$TEST" = true ]; then
    print_info "Running tests..."
    if ctest --output-on-failure --config "$BUILD_TYPE"; then
        print_success "All tests passed"
    else
        print_warning "Some tests failed"
    fi
fi

# Install if requested
if [ "$INSTALL" = true ]; then
    print_info "Installing..."
    cmake --install . --config "$BUILD_TYPE"
    print_success "Installation completed"
fi

print_success "Build process completed successfully!"
print_info "Executable location: $BUILD_DIR/examples/trading_server_example"

# Display usage information
echo ""
print_info "To run the example:"
print_info "  cd $BUILD_DIR/examples"
print_info "  ./trading_server_example"
echo ""
print_info "To run tests:"
print_info "  cd $BUILD_DIR"
print_info "  ctest --output-on-failure"
