{"server": {"id": "trading_server_001", "name": "RoboQuant Trading Server", "version": "1.0.0", "port": 8080, "network_threads": 4, "enable_web_interface": true, "enable_api_server": true}, "data": {"provider_type": "composite", "sources": ["datahub", "external_feed"], "cache_ttl_minutes": 5}, "models": {"directory": "./models", "max_concurrent_predictions": 10, "prediction_timeout_seconds": 30}, "strategies": {"config_file": "./config/strategies.json", "auto_start": true, "heartbeat_interval_seconds": 30}, "risk": {"global_limits": {"max_position_value": 10000000.0, "max_daily_loss": 500000.0, "max_drawdown": 1000000.0, "max_leverage": 3.0, "max_orders_per_second": 100}, "enable_monitoring": true, "check_interval_seconds": 10}, "logging": {"level": "info", "directory": "./logs", "enable_performance_monitoring": true, "performance_report_interval_minutes": 5}, "persistence": {"data_directory": "./data", "enable_auto_save": true, "auto_save_interval_minutes": 10}}