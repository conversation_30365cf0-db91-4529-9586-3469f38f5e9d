/**
 * @file Position.h
 * @brief Modern position management
 * <AUTHOR> Team
 * @date 2024
 */

#pragma once

#include "Types.h"
#include <vector>
#include <algorithm>

namespace RoboQuant::Trading {

/**
 * @brief Individual trade record
 */
struct Trade {
    TimePoint timestamp;
    Side side;
    Quantity quantity;
    Price price;
    Amount commission{0.0};
    std::string order_id;
    
    [[nodiscard]] Amount gross_value() const noexcept {
        return static_cast<Amount>(quantity) * price;
    }
    
    [[nodiscard]] Amount net_value() const noexcept {
        return gross_value() - commission;
    }
};

/**
 * @brief Position class representing holdings in a single asset
 */
class Position {
public:
    explicit Position(AssetId asset_id);
    ~Position() = default;

    // Copy and move semantics
    Position(const Position&) = default;
    Position& operator=(const Position&) = default;
    Position(Position&&) = default;
    Position& operator=(Position&&) = default;

    // Basic properties
    [[nodiscard]] const AssetId& asset_id() const noexcept { return asset_id_; }
    [[nodiscard]] Quantity quantity() const noexcept { return quantity_; }
    [[nodiscard]] Price average_price() const noexcept { return average_price_; }
    [[nodiscard]] Amount total_cost() const noexcept { return total_cost_; }
    [[nodiscard]] Amount total_commission() const noexcept { return total_commission_; }
    
    // Position state
    [[nodiscard]] bool is_long() const noexcept { return quantity_ > 0; }
    [[nodiscard]] bool is_short() const noexcept { return quantity_ < 0; }
    [[nodiscard]] bool is_flat() const noexcept { return quantity_ == 0; }
    [[nodiscard]] PositionSide side() const noexcept {
        if (quantity_ > 0) return PositionSide::Long;
        if (quantity_ < 0) return PositionSide::Short;
        return PositionSide::None;
    }

    // P&L calculations
    [[nodiscard]] Amount unrealized_pnl(Price current_price) const noexcept;
    [[nodiscard]] Amount realized_pnl() const noexcept { return realized_pnl_; }
    [[nodiscard]] Amount total_pnl(Price current_price) const noexcept {
        return realized_pnl() + unrealized_pnl(current_price);
    }

    // Market value
    [[nodiscard]] Amount market_value(Price current_price) const noexcept {
        return static_cast<Amount>(std::abs(quantity_)) * current_price;
    }

    // Position updates
    void add_trade(const Trade& trade);
    void add_trade(Side side, Quantity quantity, Price price, Amount commission = 0.0, 
                   const std::string& order_id = "", TimePoint timestamp = std::chrono::system_clock::now());
    
    // Position closing
    Amount close_position(Price price, Amount commission = 0.0, 
                         const std::string& order_id = "", TimePoint timestamp = std::chrono::system_clock::now());
    
    Amount reduce_position(Quantity quantity_to_reduce, Price price, Amount commission = 0.0,
                          const std::string& order_id = "", TimePoint timestamp = std::chrono::system_clock::now());

    // Trade history
    [[nodiscard]] const std::vector<Trade>& get_trades() const noexcept { return trades_; }
    [[nodiscard]] std::vector<Trade> get_trades_in_range(TimePoint start, TimePoint end) const;
    
    // Statistics
    [[nodiscard]] size_t trade_count() const noexcept { return trades_.size(); }
    [[nodiscard]] TimePoint first_trade_time() const;
    [[nodiscard]] TimePoint last_trade_time() const;
    [[nodiscard]] Duration holding_period() const;

    // Risk metrics
    [[nodiscard]] double leverage(Price current_price, Amount account_value) const noexcept;
    [[nodiscard]] Amount value_at_risk(Price current_price, double confidence_level = 0.95) const noexcept;

    // Serialization
    [[nodiscard]] std::string to_json() const;
    static std::optional<Position> from_json(const std::string& json);

    // Comparison operators
    bool operator==(const Position& other) const noexcept;
    bool operator!=(const Position& other) const noexcept { return !(*this == other); }

private:
    void update_average_price();
    void calculate_realized_pnl(const Trade& closing_trade, Quantity closed_quantity);

    AssetId asset_id_;
    Quantity quantity_{0};
    Price average_price_{0.0};
    Amount total_cost_{0.0};
    Amount total_commission_{0.0};
    Amount realized_pnl_{0.0};
    
    std::vector<Trade> trades_;
};

/**
 * @brief Position builder for fluent construction
 */
class PositionBuilder {
public:
    explicit PositionBuilder(AssetId asset_id) : position_(std::move(asset_id)) {}
    
    PositionBuilder& add_trade(Side side, Quantity quantity, Price price, Amount commission = 0.0) {
        position_.add_trade(side, quantity, price, commission);
        return *this;
    }
    
    PositionBuilder& buy(Quantity quantity, Price price, Amount commission = 0.0) {
        return add_trade(Side::Buy, quantity, price, commission);
    }
    
    PositionBuilder& sell(Quantity quantity, Price price, Amount commission = 0.0) {
        return add_trade(Side::Sell, quantity, price, commission);
    }
    
    [[nodiscard]] Position build() && { return std::move(position_); }
    [[nodiscard]] const Position& build() const& { return position_; }

private:
    Position position_;
};

/**
 * @brief Position analytics utility
 */
class PositionAnalytics {
public:
    // Performance metrics
    static double calculate_return(const Position& position, Price current_price);
    static double calculate_annualized_return(const Position& position, Price current_price);
    static double calculate_sharpe_ratio(const Position& position, Price current_price, double risk_free_rate = 0.0);
    
    // Risk metrics
    static double calculate_maximum_drawdown(const Position& position, 
                                           const std::vector<std::pair<TimePoint, Price>>& price_history);
    static double calculate_volatility(const Position& position,
                                     const std::vector<std::pair<TimePoint, Price>>& price_history);
    
    // Trade analysis
    static double calculate_win_rate(const Position& position);
    static Amount calculate_average_win(const Position& position);
    static Amount calculate_average_loss(const Position& position);
    static double calculate_profit_factor(const Position& position);
    
    // Timing analysis
    static Duration calculate_average_holding_period(const Position& position);
    static std::vector<Duration> get_holding_periods(const Position& position);
};

/**
 * @brief Position factory for creating positions from various sources
 */
class PositionFactory {
public:
    // Create from trade history
    static Position from_trades(const AssetId& asset_id, const std::vector<Trade>& trades);
    
    // Create from order fills
    static Position from_order_fills(const AssetId& asset_id, const std::vector<OrderFill>& fills);
    
    // Create simple position
    static Position create_long_position(const AssetId& asset_id, Quantity quantity, Price price);
    static Position create_short_position(const AssetId& asset_id, Quantity quantity, Price price);
};

} // namespace RoboQuant::Trading
