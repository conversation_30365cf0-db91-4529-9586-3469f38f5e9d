/**
 * @file Types.h
 * @brief Core type definitions for modern trading system
 * <AUTHOR> Team
 * @date 2024
 */

#pragma once

#include <string>
#include <chrono>
#include <memory>
#include <optional>
#include <variant>
#include <vector>
#include <unordered_map>
#include <concepts>

namespace RoboQuant::Trading {

// Forward declarations
class Portfolio;
class Strategy;
class Order;
class Position;

// Type aliases
using TimePoint = std::chrono::system_clock::time_point;
using Duration = std::chrono::milliseconds;
using AssetId = std::string;
using PortfolioId = std::string;
using OrderId = std::string;
using StrategyId = std::string;

// Numeric types
using Price = double;
using Quantity = int64_t;
using Volume = uint64_t;
using Amount = double;

// Enumerations
enum class AssetType : uint8_t {
    Stock,
    Future,
    Option,
    Bond,
    Currency,
    Crypto
};

enum class Side : uint8_t {
    Buy,
    Sell
};

enum class PositionSide : uint8_t {
    Long,
    Short,
    None
};

enum class OrderType : uint8_t {
    Market,
    Limit,
    Stop,
    StopLimit
};

enum class OrderStatus : uint8_t {
    Pending,
    PartiallyFilled,
    Filled,
    Cancelled,
    Rejected
};

enum class PositionEffect : uint8_t {
    Open,
    Close,
    CloseToday,
    CloseYesterday
};

enum class TimeInForce : uint8_t {
    Day,
    GTC,  // Good Till Cancelled
    IOC,  // Immediate Or Cancel
    FOK   // Fill Or Kill
};

enum class StrategyState : uint8_t {
    Stopped,
    Starting,
    Running,
    Stopping,
    Error
};

// Market data structures
struct QuoteData {
    AssetId asset_id;
    TimePoint timestamp;
    Price bid_price{0.0};
    Price ask_price{0.0};
    Quantity bid_size{0};
    Quantity ask_size{0};
    Price last_price{0.0};
    Volume volume{0};
    
    [[nodiscard]] Price mid_price() const noexcept {
        return (bid_price + ask_price) / 2.0;
    }
    
    [[nodiscard]] Price spread() const noexcept {
        return ask_price - bid_price;
    }
};

struct BarData {
    AssetId asset_id;
    TimePoint timestamp;
    Price open{0.0};
    Price high{0.0};
    Price low{0.0};
    Price close{0.0};
    Volume volume{0};
    Amount amount{0.0};
    
    [[nodiscard]] bool is_valid() const noexcept {
        return high >= low && high >= open && high >= close && 
               low <= open && low <= close && volume >= 0;
    }
};

// Order structures
struct OrderRequest {
    AssetId asset_id;
    Side side;
    OrderType type;
    Quantity quantity;
    std::optional<Price> price;
    std::optional<Price> stop_price;
    TimeInForce time_in_force{TimeInForce::Day};
    PositionEffect effect{PositionEffect::Open};
    std::string strategy_id;
    std::string portfolio_id;
    std::unordered_map<std::string, std::string> metadata;
};

struct OrderFill {
    OrderId order_id;
    AssetId asset_id;
    Side side;
    Quantity quantity;
    Price price;
    Amount commission{0.0};
    TimePoint timestamp;
};

// Strategy parameters
struct StrategyConfig {
    StrategyId id;
    std::string name;
    std::string description;
    bool enabled{true};
    std::unordered_map<std::string, std::variant<int, double, std::string, bool>> parameters;
    
    template<typename T>
    [[nodiscard]] std::optional<T> get_parameter(const std::string& key) const {
        auto it = parameters.find(key);
        if (it != parameters.end()) {
            if (auto* value = std::get_if<T>(&it->second)) {
                return *value;
            }
        }
        return std::nullopt;
    }
};

// Risk management
struct RiskLimits {
    Amount max_position_value{0.0};
    Amount max_daily_loss{0.0};
    Amount max_drawdown{0.0};
    double max_leverage{1.0};
    uint32_t max_orders_per_second{10};
};

// Performance metrics
struct PerformanceMetrics {
    Amount total_return{0.0};
    Amount unrealized_pnl{0.0};
    Amount realized_pnl{0.0};
    double sharpe_ratio{0.0};
    double max_drawdown{0.0};
    double win_rate{0.0};
    uint32_t total_trades{0};
    TimePoint last_updated;
};

// Concepts for type safety
template<typename T>
concept Numeric = std::integral<T> || std::floating_point<T>;

template<typename T>
concept AssetLike = requires(T t) {
    { t.asset_id } -> std::convertible_to<AssetId>;
    { t.timestamp } -> std::convertible_to<TimePoint>;
};

template<typename T>
concept OrderLike = requires(T t) {
    { t.asset_id } -> std::convertible_to<AssetId>;
    { t.side } -> std::convertible_to<Side>;
    { t.quantity } -> std::convertible_to<Quantity>;
};

// Smart pointer aliases
template<typename T>
using UniquePtr = std::unique_ptr<T>;

template<typename T>
using SharedPtr = std::shared_ptr<T>;

template<typename T>
using WeakPtr = std::weak_ptr<T>;

// Factory function helpers
template<typename T, typename... Args>
[[nodiscard]] UniquePtr<T> make_unique_ptr(Args&&... args) {
    return std::make_unique<T>(std::forward<Args>(args)...);
}

template<typename T, typename... Args>
[[nodiscard]] SharedPtr<T> make_shared_ptr(Args&&... args) {
    return std::make_shared<T>(std::forward<Args>(args)...);
}

} // namespace RoboQuant::Trading
