/**
 * @file DataProvider.cpp
 * @brief Implementation of data provider classes
 * <AUTHOR> Team
 * @date 2024
 */

#include "trading/DataProvider.h"
#include <algorithm>
#include <sstream>

namespace RoboQuant::Trading {

// FundamentalData template specializations
template<>
std::optional<double> FundamentalData::get_field<double>(const std::string& field_name) const {
    if (field_name == "market_cap") return market_cap;
    if (field_name == "pe_ratio") return pe_ratio;
    if (field_name == "pb_ratio") return pb_ratio;
    if (field_name == "dividend_yield") return dividend_yield;
    if (field_name == "roe") return roe;
    if (field_name == "roa") return roa;
    if (field_name == "debt_to_equity") return debt_to_equity;
    if (field_name == "dividend_amount") return dividend_amount;
    return std::nullopt;
}

template<>
std::optional<std::string> FundamentalData::get_field<std::string>(const std::string& field_name) const {
    if (field_name == "sector") return sector;
    if (field_name == "industry") return industry;
    if (field_name == "country") return country;
    return std::nullopt;
}

template<>
std::optional<TimePoint> FundamentalData::get_field<TimePoint>(const std::string& field_name) const {
    if (field_name == "earnings_date") return earnings_date;
    if (field_name == "ex_dividend_date") return ex_dividend_date;
    return std::nullopt;
}

// CompositeDataProvider Implementation
void CompositeDataProvider::add_market_data_provider(std::unique_ptr<DataProvider> provider) {
    std::unique_lock lock(providers_mutex_);
    market_data_providers_.push_back(std::move(provider));
}

void CompositeDataProvider::add_factor_data_provider(std::unique_ptr<FactorDataProvider> provider) {
    std::unique_lock lock(providers_mutex_);
    factor_data_providers_.push_back(std::move(provider));
}

void CompositeDataProvider::add_fundamental_data_provider(std::unique_ptr<FundamentalDataProvider> provider) {
    std::unique_lock lock(providers_mutex_);
    fundamental_data_providers_.push_back(std::move(provider));
}

std::future<std::optional<QuoteData>> CompositeDataProvider::get_quote(const AssetId& asset_id) {
    std::promise<std::optional<QuoteData>> promise;
    auto future = promise.get_future();
    
    std::shared_lock lock(providers_mutex_);
    
    // Try each provider until we get data
    for (const auto& provider : market_data_providers_) {
        try {
            auto quote_future = provider->get_quote(asset_id);
            auto quote = quote_future.get();
            
            if (quote.has_value()) {
                promise.set_value(std::move(quote));
                return future;
            }
        } catch (const std::exception&) {
            // Continue to next provider
        }
    }
    
    promise.set_value(std::nullopt);
    return future;
}

std::future<std::vector<BarData>> CompositeDataProvider::get_bars(
    const AssetId& asset_id,
    TimePoint start_time,
    TimePoint end_time,
    Duration bar_size) {
    
    std::promise<std::vector<BarData>> promise;
    auto future = promise.get_future();
    
    std::shared_lock lock(providers_mutex_);
    
    // Try each provider until we get data
    for (const auto& provider : market_data_providers_) {
        try {
            auto bars_future = provider->get_bars(asset_id, start_time, end_time, bar_size);
            auto bars = bars_future.get();
            
            if (!bars.empty()) {
                promise.set_value(std::move(bars));
                return future;
            }
        } catch (const std::exception&) {
            // Continue to next provider
        }
    }
    
    promise.set_value(std::vector<BarData>{});
    return future;
}

std::unique_ptr<DataSubscription> CompositeDataProvider::subscribe_quotes(
    const std::vector<AssetId>& asset_ids,
    QuoteCallback callback) {
    
    std::shared_lock lock(providers_mutex_);
    
    // Use the first available provider for subscription
    if (!market_data_providers_.empty()) {
        return market_data_providers_[0]->subscribe_quotes(asset_ids, std::move(callback));
    }
    
    return nullptr;
}

std::unique_ptr<DataSubscription> CompositeDataProvider::subscribe_bars(
    const std::vector<AssetId>& asset_ids,
    Duration bar_size,
    BarCallback callback) {
    
    std::shared_lock lock(providers_mutex_);
    
    // Use the first available provider for subscription
    if (!market_data_providers_.empty()) {
        return market_data_providers_[0]->subscribe_bars(asset_ids, bar_size, std::move(callback));
    }
    
    return nullptr;
}

std::future<std::vector<AssetId>> CompositeDataProvider::get_available_assets() {
    std::promise<std::vector<AssetId>> promise;
    auto future = promise.get_future();
    
    std::shared_lock lock(providers_mutex_);
    std::vector<AssetId> all_assets;
    
    // Combine assets from all providers
    for (const auto& provider : market_data_providers_) {
        try {
            auto assets_future = provider->get_available_assets();
            auto assets = assets_future.get();
            
            all_assets.insert(all_assets.end(), assets.begin(), assets.end());
        } catch (const std::exception&) {
            // Continue to next provider
        }
    }
    
    // Remove duplicates
    std::sort(all_assets.begin(), all_assets.end());
    all_assets.erase(std::unique(all_assets.begin(), all_assets.end()), all_assets.end());
    
    promise.set_value(std::move(all_assets));
    return future;
}

std::future<bool> CompositeDataProvider::is_asset_available(const AssetId& asset_id) {
    std::promise<bool> promise;
    auto future = promise.get_future();
    
    std::shared_lock lock(providers_mutex_);
    
    // Check if any provider has this asset
    for (const auto& provider : market_data_providers_) {
        try {
            auto available_future = provider->is_asset_available(asset_id);
            bool available = available_future.get();
            
            if (available) {
                promise.set_value(true);
                return future;
            }
        } catch (const std::exception&) {
            // Continue to next provider
        }
    }
    
    promise.set_value(false);
    return future;
}

std::future<bool> CompositeDataProvider::connect() {
    std::promise<bool> promise;
    auto future = promise.get_future();
    
    std::shared_lock lock(providers_mutex_);
    bool all_connected = true;
    
    // Connect all providers
    for (const auto& provider : market_data_providers_) {
        try {
            auto connect_future = provider->connect();
            if (!connect_future.get()) {
                all_connected = false;
            }
        } catch (const std::exception&) {
            all_connected = false;
        }
    }
    
    promise.set_value(all_connected);
    return future;
}

std::future<void> CompositeDataProvider::disconnect() {
    std::promise<void> promise;
    auto future = promise.get_future();
    
    std::shared_lock lock(providers_mutex_);
    
    // Disconnect all providers
    for (const auto& provider : market_data_providers_) {
        try {
            auto disconnect_future = provider->disconnect();
            disconnect_future.wait();
        } catch (const std::exception&) {
            // Continue to next provider
        }
    }
    
    promise.set_value();
    return future;
}

bool CompositeDataProvider::is_connected() const {
    std::shared_lock lock(providers_mutex_);
    
    // Check if any provider is connected
    for (const auto& provider : market_data_providers_) {
        if (provider->is_connected()) {
            return true;
        }
    }
    
    return false;
}

std::unordered_map<std::string, std::string> CompositeDataProvider::get_status() const {
    std::shared_lock lock(providers_mutex_);
    std::unordered_map<std::string, std::string> status;
    
    status["provider_count"] = std::to_string(market_data_providers_.size());
    status["connected"] = is_connected() ? "true" : "false";
    
    // Add status from each provider
    for (size_t i = 0; i < market_data_providers_.size(); ++i) {
        auto provider_status = market_data_providers_[i]->get_status();
        for (const auto& [key, value] : provider_status) {
            status["provider_" + std::to_string(i) + "_" + key] = value;
        }
    }
    
    return status;
}

// Factor data provider methods
std::future<std::optional<MarketCrossSectionData>> CompositeDataProvider::get_factor_data(
    const std::string& factor_name,
    TimePoint timestamp) {
    
    std::promise<std::optional<MarketCrossSectionData>> promise;
    auto future = promise.get_future();
    
    std::shared_lock lock(providers_mutex_);
    
    for (const auto& provider : factor_data_providers_) {
        try {
            auto factor_future = provider->get_factor_data(factor_name, timestamp);
            auto factor_data = factor_future.get();
            
            if (factor_data.has_value()) {
                promise.set_value(std::move(factor_data));
                return future;
            }
        } catch (const std::exception&) {
            // Continue to next provider
        }
    }
    
    promise.set_value(std::nullopt);
    return future;
}

std::future<std::vector<MarketCrossSectionData>> CompositeDataProvider::get_factor_history(
    const std::string& factor_name,
    TimePoint start_time,
    TimePoint end_time) {
    
    std::promise<std::vector<MarketCrossSectionData>> promise;
    auto future = promise.get_future();
    
    std::shared_lock lock(providers_mutex_);
    
    for (const auto& provider : factor_data_providers_) {
        try {
            auto history_future = provider->get_factor_history(factor_name, start_time, end_time);
            auto history = history_future.get();
            
            if (!history.empty()) {
                promise.set_value(std::move(history));
                return future;
            }
        } catch (const std::exception&) {
            // Continue to next provider
        }
    }
    
    promise.set_value(std::vector<MarketCrossSectionData>{});
    return future;
}

std::future<std::vector<std::string>> CompositeDataProvider::get_available_factors() {
    std::promise<std::vector<std::string>> promise;
    auto future = promise.get_future();
    
    std::shared_lock lock(providers_mutex_);
    std::vector<std::string> all_factors;
    
    for (const auto& provider : factor_data_providers_) {
        try {
            auto factors_future = provider->get_available_factors();
            auto factors = factors_future.get();
            
            all_factors.insert(all_factors.end(), factors.begin(), factors.end());
        } catch (const std::exception&) {
            // Continue to next provider
        }
    }
    
    // Remove duplicates
    std::sort(all_factors.begin(), all_factors.end());
    all_factors.erase(std::unique(all_factors.begin(), all_factors.end()), all_factors.end());
    
    promise.set_value(std::move(all_factors));
    return future;
}

std::future<bool> CompositeDataProvider::is_factor_available(const std::string& factor_name) {
    std::promise<bool> promise;
    auto future = promise.get_future();
    
    std::shared_lock lock(providers_mutex_);
    
    for (const auto& provider : factor_data_providers_) {
        try {
            auto available_future = provider->is_factor_available(factor_name);
            bool available = available_future.get();
            
            if (available) {
                promise.set_value(true);
                return future;
            }
        } catch (const std::exception&) {
            // Continue to next provider
        }
    }
    
    promise.set_value(false);
    return future;
}

std::unique_ptr<DataSubscription> CompositeDataProvider::subscribe_factor(
    const std::string& factor_name,
    FactorCallback callback) {
    
    std::shared_lock lock(providers_mutex_);
    
    if (!factor_data_providers_.empty()) {
        return factor_data_providers_[0]->subscribe_factor(factor_name, std::move(callback));
    }
    
    return nullptr;
}

// Fundamental data provider methods
std::future<std::optional<FundamentalData>> CompositeDataProvider::get_fundamental_data(
    const AssetId& asset_id,
    std::optional<TimePoint> as_of_date) {
    
    std::promise<std::optional<FundamentalData>> promise;
    auto future = promise.get_future();
    
    std::shared_lock lock(providers_mutex_);
    
    for (const auto& provider : fundamental_data_providers_) {
        try {
            auto fundamental_future = provider->get_fundamental_data(asset_id, as_of_date);
            auto fundamental_data = fundamental_future.get();
            
            if (fundamental_data.has_value()) {
                promise.set_value(std::move(fundamental_data));
                return future;
            }
        } catch (const std::exception&) {
            // Continue to next provider
        }
    }
    
    promise.set_value(std::nullopt);
    return future;
}

std::future<std::vector<FundamentalData>> CompositeDataProvider::get_fundamental_history(
    const AssetId& asset_id,
    TimePoint start_time,
    TimePoint end_time) {
    
    std::promise<std::vector<FundamentalData>> promise;
    auto future = promise.get_future();
    
    std::shared_lock lock(providers_mutex_);
    
    for (const auto& provider : fundamental_data_providers_) {
        try {
            auto history_future = provider->get_fundamental_history(asset_id, start_time, end_time);
            auto history = history_future.get();
            
            if (!history.empty()) {
                promise.set_value(std::move(history));
                return future;
            }
        } catch (const std::exception&) {
            // Continue to next provider
        }
    }
    
    promise.set_value(std::vector<FundamentalData>{});
    return future;
}

std::future<std::vector<AssetId>> CompositeDataProvider::screen_assets(
    const std::unordered_map<std::string, std::variant<double, std::string>>& criteria) {
    
    std::promise<std::vector<AssetId>> promise;
    auto future = promise.get_future();
    
    std::shared_lock lock(providers_mutex_);
    std::vector<AssetId> all_results;
    
    for (const auto& provider : fundamental_data_providers_) {
        try {
            auto screen_future = provider->screen_assets(criteria);
            auto results = screen_future.get();
            
            all_results.insert(all_results.end(), results.begin(), results.end());
        } catch (const std::exception&) {
            // Continue to next provider
        }
    }
    
    // Remove duplicates
    std::sort(all_results.begin(), all_results.end());
    all_results.erase(std::unique(all_results.begin(), all_results.end()), all_results.end());
    
    promise.set_value(std::move(all_results));
    return future;
}

std::future<std::vector<FundamentalData>> CompositeDataProvider::get_upcoming_earnings(
    std::optional<TimePoint> start_date,
    std::optional<TimePoint> end_date) {
    
    std::promise<std::vector<FundamentalData>> promise;
    auto future = promise.get_future();
    
    std::shared_lock lock(providers_mutex_);
    std::vector<FundamentalData> all_earnings;
    
    for (const auto& provider : fundamental_data_providers_) {
        try {
            auto earnings_future = provider->get_upcoming_earnings(start_date, end_date);
            auto earnings = earnings_future.get();
            
            all_earnings.insert(all_earnings.end(), earnings.begin(), earnings.end());
        } catch (const std::exception&) {
            // Continue to next provider
        }
    }
    
    promise.set_value(std::move(all_earnings));
    return future;
}

std::future<std::vector<FundamentalData>> CompositeDataProvider::get_upcoming_dividends(
    std::optional<TimePoint> start_date,
    std::optional<TimePoint> end_date) {
    
    std::promise<std::vector<FundamentalData>> promise;
    auto future = promise.get_future();
    
    std::shared_lock lock(providers_mutex_);
    std::vector<FundamentalData> all_dividends;
    
    for (const auto& provider : fundamental_data_providers_) {
        try {
            auto dividends_future = provider->get_upcoming_dividends(start_date, end_date);
            auto dividends = dividends_future.get();
            
            all_dividends.insert(all_dividends.end(), dividends.begin(), dividends.end());
        } catch (const std::exception&) {
            // Continue to next provider
        }
    }
    
    promise.set_value(std::move(all_dividends));
    return future;
}

} // namespace RoboQuant::Trading
