/**
 * @file FuturesStrategy.h
 * @brief Modern futures trading strategy implementation
 * <AUTHOR> Team
 * @date 2024
 */

#pragma once

#include "../Strategy.h"
#include "../Types.h"
#include <unordered_set>
#include <memory>

namespace RoboQuant::Trading::Strategies {

/**
 * @brief Configuration for futures strategy
 */
struct FuturesStrategyConfig {
    // Basic parameters
    double entry_threshold{0.02};      // Entry signal threshold
    double exit_threshold{0.01};       // Exit signal threshold
    double stop_loss_pct{0.05};        // Stop loss percentage
    double take_profit_pct{0.10};      // Take profit percentage
    
    // Position sizing
    double max_position_size{1000000.0}; // Maximum position value
    double risk_per_trade{0.02};         // Risk per trade as % of portfolio
    
    // Timing parameters
    Duration min_hold_time{std::chrono::minutes(5)};   // Minimum holding time
    Duration max_hold_time{std::chrono::hours(24)};    // Maximum holding time
    
    // Market timing
    bool enable_market_timing{true};
    std::string market_timing_model;
    
    // Risk management
    bool enable_position_sizing{true};
    bool enable_stop_loss{true};
    bool enable_take_profit{true};
    
    // Asset filtering
    std::unordered_set<std::string> allowed_assets;
    std::unordered_set<std::string> blocked_assets;
    
    // Model parameters
    std::string prediction_model;
    double min_prediction_confidence{0.6};
    
    // Execution parameters
    bool use_market_orders{false};
    double limit_order_offset{0.001}; // Offset for limit orders
    Duration order_timeout{std::chrono::minutes(5)};
};

/**
 * @brief Futures strategy data for each asset
 */
struct FuturesStrategyData {
    AssetId asset_id;
    
    // Predictions
    double pred_long{0.0};
    double pred_short{0.0};
    double pred_confidence{0.0};
    
    // Technical indicators
    double momentum{0.0};
    double volatility{0.0};
    double trend_strength{0.0};
    
    // Position info
    std::optional<TimePoint> entry_time;
    std::optional<Price> entry_price;
    std::optional<Price> stop_loss_price;
    std::optional<Price> take_profit_price;
    
    // Risk metrics
    double current_risk{0.0};
    double max_risk{0.0};
    
    // Performance tracking
    double unrealized_pnl{0.0};
    double realized_pnl{0.0};
    uint32_t trade_count{0};
    
    [[nodiscard]] bool has_position() const noexcept {
        return entry_time.has_value();
    }
    
    [[nodiscard]] Duration holding_time() const {
        if (!entry_time) return Duration::zero();
        return std::chrono::duration_cast<Duration>(
            std::chrono::system_clock::now() - *entry_time
        );
    }
};

/**
 * @brief Modern futures trading strategy
 */
class FuturesStrategy : public Strategy {
public:
    explicit FuturesStrategy(StrategyConfig config);
    ~FuturesStrategy() override = default;

    // Strategy identification
    static std::string strategy_name() { return "FuturesStrategy"; }
    
    // Lifecycle methods
    std::future<bool> initialize() override;
    std::future<void> start() override;
    std::future<void> stop() override;
    void shutdown() override;

    // Event handlers
    void on_market_data(const QuoteData& quote) override;
    void on_bar_data(const BarData& bar) override;
    void on_order_fill(const OrderFill& fill) override;
    void on_order_update(const Order& order) override;
    void on_timer(const std::string& timer_id) override;

    // Configuration
    void set_futures_config(const FuturesStrategyConfig& config);
    [[nodiscard]] const FuturesStrategyConfig& get_futures_config() const noexcept;

    // Strategy data access
    [[nodiscard]] std::optional<FuturesStrategyData> get_strategy_data(const AssetId& asset_id) const;
    [[nodiscard]] std::vector<FuturesStrategyData> get_all_strategy_data() const;

    // Manual operations
    std::future<bool> manual_open_position(const AssetId& asset_id, PositionSide side, 
                                          Quantity quantity = 0, bool force = false);
    std::future<bool> manual_close_position(const AssetId& asset_id, PositionSide side = PositionSide::None);
    std::future<bool> close_all_positions();

    // Risk management
    void update_risk_limits(const RiskLimits& limits);
    [[nodiscard]] bool check_position_risk(const AssetId& asset_id, const OrderRequest& request) const;

private:
    // Strategy logic
    void process_asset(const AssetId& asset_id, const QuoteData& quote);
    void process_asset(const AssetId& asset_id, const BarData& bar);
    
    // Signal generation
    [[nodiscard]] double generate_long_signal(const AssetId& asset_id) const;
    [[nodiscard]] double generate_short_signal(const AssetId& asset_id) const;
    [[nodiscard]] bool should_enter_long(const AssetId& asset_id) const;
    [[nodiscard]] bool should_enter_short(const AssetId& asset_id) const;
    [[nodiscard]] bool should_exit_position(const AssetId& asset_id, PositionSide side) const;
    
    // Position management
    std::future<bool> open_long_position(const AssetId& asset_id, Quantity quantity = 0);
    std::future<bool> open_short_position(const AssetId& asset_id, Quantity quantity = 0);
    std::future<bool> close_position(const AssetId& asset_id, PositionSide side);
    
    // Risk management
    [[nodiscard]] Quantity calculate_position_size(const AssetId& asset_id, Price price) const;
    [[nodiscard]] Price calculate_stop_loss_price(const AssetId& asset_id, PositionSide side, Price entry_price) const;
    [[nodiscard]] Price calculate_take_profit_price(const AssetId& asset_id, PositionSide side, Price entry_price) const;
    
    // Order management
    std::future<std::optional<OrderId>> submit_market_order(const AssetId& asset_id, Side side, Quantity quantity);
    std::future<std::optional<OrderId>> submit_limit_order(const AssetId& asset_id, Side side, 
                                                          Quantity quantity, Price price);
    
    // Data management
    void update_strategy_data(const AssetId& asset_id, const QuoteData& quote);
    void update_strategy_data(const AssetId& asset_id, const BarData& bar);
    void initialize_strategy_data(const AssetId& asset_id);
    
    // Model integration
    [[nodiscard]] double get_model_prediction(const AssetId& asset_id, const std::string& model_id) const;
    [[nodiscard]] bool is_market_timing_favorable() const;
    
    // Utility methods
    [[nodiscard]] bool is_asset_allowed(const AssetId& asset_id) const;
    [[nodiscard]] bool is_trading_time() const;
    [[nodiscard]] Price get_current_price(const AssetId& asset_id) const;
    
    // Timer management
    void setup_timers();
    void on_risk_check_timer();
    void on_position_timeout_timer();
    void on_market_close_timer();

private:
    FuturesStrategyConfig futures_config_;
    
    mutable std::shared_mutex strategy_data_mutex_;
    std::unordered_map<AssetId, FuturesStrategyData> strategy_data_;
    
    mutable std::shared_mutex price_cache_mutex_;
    std::unordered_map<AssetId, QuoteData> price_cache_;
    
    // Timers
    std::vector<std::string> active_timers_;
    
    // Performance tracking
    mutable std::mutex performance_mutex_;
    std::unordered_map<AssetId, PerformanceMetrics> asset_performance_;
};

/**
 * @brief Futures strategy factory
 */
class FuturesStrategyFactory : public StrategyFactory {
public:
    UniquePtr<Strategy> create_strategy(const StrategyConfig& config) override;
    std::vector<std::string> get_supported_strategies() const override;
};

} // namespace RoboQuant::Trading::Strategies
