// SecurityService Implementation - Security information management service
#include "services/SecurityService_Enhanced.h"
#include <algorithm>
#include <chrono>
#include <thread>
#include <regex>

namespace DataHub::Services {

// SecurityService Implementation
SecurityService::SecurityService(
    std::shared_ptr<Data::ISecurityRepository> security_repo,
    SecurityServiceConfig config)
    : config_(std::move(config))
    , running_(false)
    , security_repo_(std::move(security_repo))
    , stop_maintenance_(false) {
}

SecurityService::~SecurityService() {
    if (running_.load()) {
        stop();
    }
}

Core::Result<void> SecurityService::start() {
    if (running_.load()) {
        return Core::make_error<void>(Core::ErrorCode::InvalidOperation, "SecurityService already running");
    }
    
    if (!security_repo_) {
        return Core::make_error<void>(Core::ErrorCode::InvalidOperation, "Repository not available");
    }
    
    stop_maintenance_ = false;
    
    // Start maintenance thread
    if (config_.enable_auto_update) {
        maintenance_thread_ = std::make_unique<std::thread>(&SecurityService::maintenance_loop, this);
    }
    
    running_ = true;
    return Core::make_success();
}

Core::Result<void> SecurityService::stop() {
    if (!running_.load()) {
        return Core::make_success();
    }
    
    stop_maintenance_ = true;
    
    // Stop maintenance thread
    if (maintenance_thread_ && maintenance_thread_->joinable()) {
        maintenance_thread_->join();
    }
    maintenance_thread_.reset();
    
    running_ = false;
    return Core::make_success();
}

bool SecurityService::is_running() const noexcept {
    return running_.load();
}

// Security management
Core::Result<void> SecurityService::add_security(const Core::SecurityInfo& security) {
    if (!running_.load()) {
        return Core::make_error<void>(Core::ErrorCode::ServiceNotAvailable, "Service not running");
    }
    
    if (security.symbol.empty()) {
        return Core::make_error<void>(Core::ErrorCode::InvalidArgument, "Symbol cannot be empty");
    }
    
    // Validate security data
    if (config_.enable_strict_validation) {
        auto validation_result = validate_security(security);
        if (!validation_result.empty()) {
            return Core::make_error<void>(Core::ErrorCode::InvalidArgument, "Security validation failed");
        }
    }
    
    // Save to repository
    // Implementation depends on repository interface
    
    // Update cache and indices
    update_security_cache(security);
    update_indices(security);
    
    return Core::make_success();
}

Core::Result<void> SecurityService::update_security(const Core::SecurityInfo& security) {
    if (!running_.load()) {
        return Core::make_error<void>(Core::ErrorCode::ServiceNotAvailable, "Service not running");
    }
    
    if (security.symbol.empty()) {
        return Core::make_error<void>(Core::ErrorCode::InvalidArgument, "Symbol cannot be empty");
    }
    
    // Validate security data
    if (config_.enable_strict_validation) {
        auto validation_result = validate_security(security);
        if (!validation_result.empty()) {
            return Core::make_error<void>(Core::ErrorCode::InvalidArgument, "Security validation failed");
        }
    }
    
    // Update in repository
    // Implementation depends on repository interface
    
    // Update cache and indices
    update_security_cache(security);
    update_indices(security);
    
    return Core::make_success();
}

Core::Result<void> SecurityService::remove_security(const Core::Symbol& symbol) {
    if (!running_.load()) {
        return Core::make_error<void>(Core::ErrorCode::ServiceNotAvailable, "Service not running");
    }
    
    if (symbol.empty()) {
        return Core::make_error<void>(Core::ErrorCode::InvalidArgument, "Symbol cannot be empty");
    }
    
    // Remove from repository
    // Implementation depends on repository interface
    
    // Remove from cache and indices
    remove_from_cache(symbol);
    remove_from_indices(symbol);
    
    return Core::make_success();
}

Core::Result<Core::SecurityInfo> SecurityService::get_security(const Core::Symbol& symbol) {
    if (!running_.load()) {
        return Core::make_error<Core::SecurityInfo>(Core::ErrorCode::ServiceNotAvailable, "Service not running");
    }
    
    if (symbol.empty()) {
        return Core::make_error<Core::SecurityInfo>(Core::ErrorCode::InvalidArgument, "Symbol cannot be empty");
    }
    
    // Check cache first
    {
        std::lock_guard<std::mutex> lock(cache_mutex_);
        auto it = security_cache_.find(symbol);
        if (it != security_cache_.end()) {
            return Core::make_success(it->second);
        }
    }
    
    // Query from repository
    // Implementation depends on repository interface
    
    return Core::make_error<Core::SecurityInfo>(Core::ErrorCode::DataNotFound, "Security not found");
}

Core::Result<std::vector<Core::SecurityInfo>> SecurityService::get_securities(const Core::SymbolVector& symbols) {
    if (!running_.load()) {
        return Core::make_error<std::vector<Core::SecurityInfo>>(Core::ErrorCode::ServiceNotAvailable, "Service not running");
    }
    
    std::vector<Core::SecurityInfo> results;
    results.reserve(symbols.size());
    
    for (const auto& symbol : symbols) {
        auto security_result = get_security(symbol);
        if (security_result.is_success()) {
            results.push_back(security_result.value());
        }
    }
    
    return Core::make_success(results);
}

// Search and filtering
Core::Result<std::vector<Core::SecurityInfo>> SecurityService::search_securities(const SecuritySearchCriteria& criteria) {
    if (!running_.load()) {
        return Core::make_error<std::vector<Core::SecurityInfo>>(Core::ErrorCode::ServiceNotAvailable, "Service not running");
    }
    
    std::vector<Core::SecurityInfo> results;
    
    // Search in cache first for better performance
    {
        std::lock_guard<std::mutex> lock(cache_mutex_);
        for (const auto& [symbol, security] : security_cache_) {
            if (matches_criteria(security, criteria)) {
                results.push_back(security);
                
                if (results.size() >= criteria.limit) {
                    break;
                }
            }
        }
    }
    
    // If cache doesn't have enough results, query repository
    if (results.size() < criteria.limit) {
        // Implementation would query repository for additional results
    }
    
    return Core::make_success(results);
}

Core::Result<std::vector<Core::SecurityInfo>> SecurityService::filter_securities(
    const std::vector<Core::SecurityInfo>& securities,
    const SecurityFilter& filter) {
    
    std::vector<Core::SecurityInfo> results;
    
    for (const auto& security : securities) {
        bool matches = true;
        
        // Apply filters
        if (filter.market && security.market != *filter.market) {
            matches = false;
        }
        
        if (filter.security_type && security.type != *filter.security_type) {
            matches = false;
        }
        
        if (filter.currency && security.currency != *filter.currency) {
            matches = false;
        }
        
        if (!filter.sector.empty() && security.sector != filter.sector) {
            matches = false;
        }
        
        if (!filter.industry.empty() && security.industry != filter.industry) {
            matches = false;
        }
        
        if (filter.is_active && security.is_active != *filter.is_active) {
            matches = false;
        }
        
        if (matches) {
            results.push_back(security);
        }
    }
    
    return Core::make_success(results);
}

// Block/Sector management
Core::Result<void> SecurityService::add_block(const Core::BlockInfo& block) {
    if (!running_.load()) {
        return Core::make_error<void>(Core::ErrorCode::ServiceNotAvailable, "Service not running");
    }
    
    if (block.name.empty()) {
        return Core::make_error<void>(Core::ErrorCode::InvalidArgument, "Block name cannot be empty");
    }
    
    // Save to repository
    // Implementation depends on repository interface
    
    // Update cache
    {
        std::lock_guard<std::mutex> lock(cache_mutex_);
        block_cache_[block.name] = block;
    }
    
    return Core::make_success();
}

Core::Result<Core::BlockInfo> SecurityService::get_block(const std::string& name) {
    if (!running_.load()) {
        return Core::make_error<Core::BlockInfo>(Core::ErrorCode::ServiceNotAvailable, "Service not running");
    }
    
    if (name.empty()) {
        return Core::make_error<Core::BlockInfo>(Core::ErrorCode::InvalidArgument, "Block name cannot be empty");
    }
    
    // Check cache first
    {
        std::lock_guard<std::mutex> lock(cache_mutex_);
        auto it = block_cache_.find(name);
        if (it != block_cache_.end()) {
            return Core::make_success(it->second);
        }
    }
    
    // Query from repository
    // Implementation depends on repository interface
    
    return Core::make_error<Core::BlockInfo>(Core::ErrorCode::DataNotFound, "Block not found");
}

Core::Result<std::vector<Core::BlockInfo>> SecurityService::get_all_blocks() {
    if (!running_.load()) {
        return Core::make_error<std::vector<Core::BlockInfo>>(Core::ErrorCode::ServiceNotAvailable, "Service not running");
    }
    
    std::vector<Core::BlockInfo> results;
    
    // Get from cache
    {
        std::lock_guard<std::mutex> lock(cache_mutex_);
        results.reserve(block_cache_.size());
        for (const auto& [name, block] : block_cache_) {
            results.push_back(block);
        }
    }
    
    return Core::make_success(results);
}

Core::Result<Core::SymbolVector> SecurityService::get_block_securities(const std::string& block_name) {
    if (!running_.load()) {
        return Core::make_error<Core::SymbolVector>(Core::ErrorCode::ServiceNotAvailable, "Service not running");
    }
    
    auto block_result = get_block(block_name);
    if (!block_result.is_success()) {
        return Core::make_error<Core::SymbolVector>(block_result.error().code, block_result.error().message);
    }
    
    return Core::make_success(block_result.value().securities);
}

// Statistics and information
Core::Result<SecurityStatistics> SecurityService::get_statistics() {
    if (!running_.load()) {
        return Core::make_error<SecurityStatistics>(Core::ErrorCode::ServiceNotAvailable, "Service not running");
    }
    
    SecurityStatistics stats;
    
    {
        std::lock_guard<std::mutex> lock(cache_mutex_);
        stats.total_securities = security_cache_.size();
        stats.total_blocks = block_cache_.size();
        
        // Count by market
        for (const auto& [symbol, security] : security_cache_) {
            stats.securities_by_market[security.market]++;
            
            if (security.is_active) {
                stats.active_securities++;
            }
        }
    }
    
    return Core::make_success(stats);
}

Core::Result<Core::SymbolVector> SecurityService::get_symbols_by_market(Core::Market market) {
    if (!running_.load()) {
        return Core::make_error<Core::SymbolVector>(Core::ErrorCode::ServiceNotAvailable, "Service not running");
    }
    
    Core::SymbolVector results;
    
    {
        std::lock_guard<std::mutex> lock(cache_mutex_);
        for (const auto& [symbol, security] : security_cache_) {
            if (security.market == market) {
                results.push_back(symbol);
            }
        }
    }
    
    return Core::make_success(results);
}

Core::Result<Core::SymbolVector> SecurityService::get_symbols_by_type(Core::SecurityType type) {
    if (!running_.load()) {
        return Core::make_error<Core::SymbolVector>(Core::ErrorCode::ServiceNotAvailable, "Service not running");
    }
    
    Core::SymbolVector results;
    
    {
        std::lock_guard<std::mutex> lock(cache_mutex_);
        for (const auto& [symbol, security] : security_cache_) {
            if (security.type == type) {
                results.push_back(symbol);
            }
        }
    }
    
    return Core::make_success(results);
}

// Configuration
Core::Result<void> SecurityService::update_config(const SecurityServiceConfig& config) {
    config_ = config;
    return Core::make_success();
}

Core::Result<SecurityServiceConfig> SecurityService::get_config() const {
    return Core::make_success(config_);
}

// Private methods
void SecurityService::maintenance_loop() {
    while (!stop_maintenance_.load()) {
        try {
            cleanup_cache();
            update_indices();
            
            if (config_.enable_auto_update) {
                auto_sync();
            }
            
            // Sleep for update interval
            std::this_thread::sleep_for(std::chrono::hours(config_.update_interval_hours));
        } catch (const std::exception& e) {
            // Log error but continue
        }
    }
}

void SecurityService::update_indices() {
    // Update search indices for better performance
    // Implementation would rebuild search indices
}

void SecurityService::cleanup_cache() {
    std::lock_guard<std::mutex> lock(cache_mutex_);
    
    // Simple cache cleanup - remove entries if cache is too large
    if (security_cache_.size() > config_.max_cache_size) {
        auto it = security_cache_.begin();
        std::advance(it, security_cache_.size() / 2);
        security_cache_.erase(security_cache_.begin(), it);
    }
}

void SecurityService::auto_sync() {
    // Implementation would sync with external data sources
}

bool SecurityService::matches_criteria(const Core::SecurityInfo& security, const SecuritySearchCriteria& criteria) const {
    // Check symbol pattern
    if (!criteria.symbol_pattern.empty()) {
        if (!matches_pattern(security.symbol, criteria.symbol_pattern)) {
            return false;
        }
    }
    
    // Check name pattern
    if (!criteria.name_pattern.empty()) {
        if (!matches_pattern(security.name, criteria.name_pattern)) {
            return false;
        }
    }
    
    // Check market
    if (criteria.market && security.market != *criteria.market) {
        return false;
    }
    
    // Check security type
    if (criteria.security_type && security.type != *criteria.security_type) {
        return false;
    }
    
    // Check sector
    if (!criteria.sector.empty() && security.sector != criteria.sector) {
        return false;
    }
    
    // Check active status
    if (criteria.is_active && security.is_active != *criteria.is_active) {
        return false;
    }
    
    return true;
}

bool SecurityService::matches_pattern(const std::string& text, const std::string& pattern) const {
    try {
        std::regex regex_pattern(pattern, std::regex_constants::icase);
        return std::regex_search(text, regex_pattern);
    } catch (const std::exception& e) {
        // If regex fails, fall back to simple substring search
        std::string lower_text = text;
        std::string lower_pattern = pattern;
        std::transform(lower_text.begin(), lower_text.end(), lower_text.begin(), ::tolower);
        std::transform(lower_pattern.begin(), lower_pattern.end(), lower_pattern.begin(), ::tolower);
        return lower_text.find(lower_pattern) != std::string::npos;
    }
}

void SecurityService::update_security_cache(const Core::SecurityInfo& security) {
    std::lock_guard<std::mutex> lock(cache_mutex_);
    security_cache_[security.symbol] = security;
}

void SecurityService::remove_from_cache(const Core::Symbol& symbol) {
    std::lock_guard<std::mutex> lock(cache_mutex_);
    security_cache_.erase(symbol);
}

void SecurityService::update_indices(const Core::SecurityInfo& security) {
    // Update search indices
    // Implementation would update various search indices
}

void SecurityService::remove_from_indices(const Core::Symbol& symbol) {
    // Remove from search indices
    // Implementation would remove from various search indices
}

std::vector<std::string> SecurityService::validate_security(const Core::SecurityInfo& security) const {
    std::vector<std::string> errors;
    
    if (security.symbol.empty()) {
        errors.push_back("Symbol cannot be empty");
    }
    
    if (security.name.empty()) {
        errors.push_back("Name cannot be empty");
    }
    
    // Additional validation rules can be added here
    
    return errors;
}

// Factory methods
std::unique_ptr<ISecurityService> SecurityServiceFactory::create(
    std::shared_ptr<Data::ISecurityRepository> security_repo,
    const SecurityServiceConfig& config) {
    
    return std::make_unique<SecurityService>(std::move(security_repo), config);
}

std::unique_ptr<ISecurityService> SecurityServiceFactory::create_with_validation(
    std::shared_ptr<Data::ISecurityRepository> security_repo,
    bool strict_validation) {
    
    SecurityServiceConfig config;
    config.enable_strict_validation = strict_validation;
    
    return create(std::move(security_repo), config);
}

} // namespace DataHub::Services
