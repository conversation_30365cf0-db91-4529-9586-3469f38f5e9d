/**
 * @file OrderManager.h
 * @brief Modern order management system
 * <AUTHOR> Team
 * @date 2024
 */

#pragma once

#include "Types.h"
#include "Events.h"
#include <queue>
#include <future>
#include <atomic>
#include <condition_variable>

namespace RoboQuant::Trading {

/**
 * @brief Order class representing a trading order
 */
class Order {
public:
    explicit Order(OrderId id, OrderRequest request);
    ~Order() = default;

    // Copy and move semantics
    Order(const Order&) = default;
    Order& operator=(const Order&) = default;
    Order(Order&&) = default;
    Order& operator=(Order&&) = default;

    // Basic properties
    [[nodiscard]] const OrderId& id() const noexcept { return id_; }
    [[nodiscard]] const OrderRequest& request() const noexcept { return request_; }
    [[nodiscard]] OrderStatus status() const noexcept { return status_; }
    [[nodiscard]] TimePoint created_time() const noexcept { return created_time_; }
    [[nodiscard]] std::optional<TimePoint> updated_time() const noexcept { return updated_time_; }

    // Execution details
    [[nodiscard]] Quantity filled_quantity() const noexcept { return filled_quantity_; }
    [[nodiscard]] Quantity remaining_quantity() const noexcept { 
        return request_.quantity - filled_quantity_; 
    }
    [[nodiscard]] Price average_fill_price() const noexcept { return average_fill_price_; }
    [[nodiscard]] Amount total_commission() const noexcept { return total_commission_; }

    // Status checks
    [[nodiscard]] bool is_pending() const noexcept { return status_ == OrderStatus::Pending; }
    [[nodiscard]] bool is_filled() const noexcept { return status_ == OrderStatus::Filled; }
    [[nodiscard]] bool is_partially_filled() const noexcept { return status_ == OrderStatus::PartiallyFilled; }
    [[nodiscard]] bool is_cancelled() const noexcept { return status_ == OrderStatus::Cancelled; }
    [[nodiscard]] bool is_rejected() const noexcept { return status_ == OrderStatus::Rejected; }
    [[nodiscard]] bool is_active() const noexcept { 
        return status_ == OrderStatus::Pending || status_ == OrderStatus::PartiallyFilled; 
    }

    // Order updates
    void add_fill(const OrderFill& fill);
    void cancel(const std::string& reason = "");
    void reject(const std::string& reason = "");

    // Fill history
    [[nodiscard]] const std::vector<OrderFill>& fills() const noexcept { return fills_; }
    [[nodiscard]] std::optional<std::string> cancel_reason() const noexcept { return cancel_reason_; }
    [[nodiscard]] std::optional<std::string> reject_reason() const noexcept { return reject_reason_; }

    // Serialization
    [[nodiscard]] std::string to_json() const;
    static std::optional<Order> from_json(const std::string& json);

private:
    void update_status();
    void update_fill_statistics();

    OrderId id_;
    OrderRequest request_;
    OrderStatus status_{OrderStatus::Pending};
    TimePoint created_time_;
    std::optional<TimePoint> updated_time_;

    Quantity filled_quantity_{0};
    Price average_fill_price_{0.0};
    Amount total_commission_{0.0};
    
    std::vector<OrderFill> fills_;
    std::optional<std::string> cancel_reason_;
    std::optional<std::string> reject_reason_;
};

/**
 * @brief Order execution interface
 */
class OrderExecutor {
public:
    virtual ~OrderExecutor() = default;
    
    virtual std::future<bool> submit_order(const Order& order) = 0;
    virtual std::future<bool> cancel_order(const OrderId& order_id) = 0;
    virtual std::future<bool> modify_order(const OrderId& order_id, const OrderRequest& new_request) = 0;
    
    // Status queries
    virtual std::future<std::optional<OrderStatus>> get_order_status(const OrderId& order_id) = 0;
    virtual std::future<std::vector<Order>> get_active_orders() = 0;
};

/**
 * @brief Order validation interface
 */
class OrderValidator {
public:
    virtual ~OrderValidator() = default;
    
    struct ValidationResult {
        bool is_valid{false};
        std::string error_message;
        
        [[nodiscard]] explicit operator bool() const noexcept { return is_valid; }
    };
    
    virtual ValidationResult validate_order(const OrderRequest& request) const = 0;
};

/**
 * @brief Composite order validator
 */
class CompositeOrderValidator : public OrderValidator {
public:
    void add_validator(UniquePtr<OrderValidator> validator);
    ValidationResult validate_order(const OrderRequest& request) const override;

private:
    std::vector<UniquePtr<OrderValidator>> validators_;
};

/**
 * @brief Basic order validators
 */
class BasicOrderValidator : public OrderValidator {
public:
    ValidationResult validate_order(const OrderRequest& request) const override;
};

class RiskOrderValidator : public OrderValidator {
public:
    explicit RiskOrderValidator(RiskLimits limits) : limits_(limits) {}
    ValidationResult validate_order(const OrderRequest& request) const override;

private:
    RiskLimits limits_;
};

/**
 * @brief Order manager for centralized order handling
 */
class OrderManager {
public:
    explicit OrderManager(UniquePtr<OrderExecutor> executor, UniquePtr<OrderValidator> validator = nullptr);
    ~OrderManager();

    // Non-copyable, movable
    OrderManager(const OrderManager&) = delete;
    OrderManager& operator=(const OrderManager&) = delete;
    OrderManager(OrderManager&&) = default;
    OrderManager& operator=(OrderManager&&) = default;

    // Order operations
    std::future<std::optional<OrderId>> submit_order(const OrderRequest& request);
    std::future<bool> cancel_order(const OrderId& order_id);
    std::future<bool> cancel_all_orders();
    std::future<bool> cancel_orders_for_asset(const AssetId& asset_id);

    // Order queries
    [[nodiscard]] std::optional<Order> get_order(const OrderId& order_id) const;
    [[nodiscard]] std::vector<Order> get_orders_for_asset(const AssetId& asset_id) const;
    [[nodiscard]] std::vector<Order> get_active_orders() const;
    [[nodiscard]] std::vector<Order> get_all_orders() const;

    // Order updates (called by execution system)
    void update_order_fill(const OrderFill& fill);
    void update_order_status(const OrderId& order_id, OrderStatus status, const std::string& reason = "");

    // Event handling
    using OrderEventCallback = std::function<void(const Order&, const std::string& event_type)>;
    void set_order_event_callback(OrderEventCallback callback);

    // Statistics
    [[nodiscard]] size_t total_order_count() const;
    [[nodiscard]] size_t active_order_count() const;
    [[nodiscard]] Amount total_commission_paid() const;

    // Control
    void start();
    void stop();
    [[nodiscard]] bool is_running() const noexcept;

private:
    void process_order_queue();
    void notify_order_event(const Order& order, const std::string& event_type);
    OrderId generate_order_id();

    UniquePtr<OrderExecutor> executor_;
    UniquePtr<OrderValidator> validator_;
    
    std::atomic<bool> running_{false};
    std::thread worker_thread_;
    
    mutable std::shared_mutex orders_mutex_;
    std::unordered_map<OrderId, Order> orders_;
    
    std::mutex queue_mutex_;
    std::queue<std::function<void()>> order_queue_;
    std::condition_variable queue_condition_;
    
    std::mutex callback_mutex_;
    OrderEventCallback order_event_callback_;
    
    std::atomic<uint64_t> order_counter_{0};
};

/**
 * @brief Simulation order executor for backtesting
 */
class SimulationOrderExecutor : public OrderExecutor {
public:
    explicit SimulationOrderExecutor(std::function<Price(const AssetId&)> price_provider);
    
    std::future<bool> submit_order(const Order& order) override;
    std::future<bool> cancel_order(const OrderId& order_id) override;
    std::future<bool> modify_order(const OrderId& order_id, const OrderRequest& new_request) override;
    
    std::future<std::optional<OrderStatus>> get_order_status(const OrderId& order_id) override;
    std::future<std::vector<Order>> get_active_orders() override;
    
    // Simulation controls
    void set_fill_delay(Duration delay) { fill_delay_ = delay; }
    void set_slippage(double slippage) { slippage_ = slippage; }
    void set_commission_rate(double rate) { commission_rate_ = rate; }

private:
    std::function<Price(const AssetId&)> price_provider_;
    Duration fill_delay_{std::chrono::milliseconds(100)};
    double slippage_{0.001}; // 0.1% default slippage
    double commission_rate_{0.001}; // 0.1% default commission
    
    mutable std::mutex orders_mutex_;
    std::unordered_map<OrderId, Order> pending_orders_;
};

} // namespace RoboQuant::Trading
