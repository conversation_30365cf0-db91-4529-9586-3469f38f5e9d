#pragma once

#include "../core/Types.h"
#include "../core/MarketData.h"
#include "../services/DataHubManager.h"
#include <vector>
#include <unordered_map>
#include <functional>
#include <memory>

namespace DataHub::Analytics {

// Technical indicator types
enum class IndicatorType : std::uint8_t {
    SMA = 1,        // Simple Moving Average
    EMA = 2,        // Exponential Moving Average
    RSI = 3,        // Relative Strength Index
    MACD = 4,       // Moving Average Convergence Divergence
    Bollinger = 5,  // Bollinger Bands
    Stochastic = 6, // Stochastic Oscillator
    ATR = 7,        // Average True Range
    Volume = 8      // Volume indicators
};

// Indicator result structure
struct IndicatorResult {
    IndicatorType type;
    Core::Symbol symbol;
    Core::Timestamp timestamp;
    std::unordered_map<std::string, double> values;
    
    // Helper methods
    double get_value(const std::string& key, double default_value = 0.0) const {
        auto it = values.find(key);
        return it != values.end() ? it->second : default_value;
    }
    
    void set_value(const std::string& key, double value) {
        values[key] = value;
    }
};

using IndicatorResultVector = std::vector<IndicatorResult>;

// Market statistics
struct MarketStatistics {
    Core::Symbol symbol;
    Core::Timestamp start_time;
    Core::Timestamp end_time;
    
    // Price statistics
    double min_price{0.0};
    double max_price{0.0};
    double mean_price{0.0};
    double median_price{0.0};
    double std_deviation{0.0};
    double variance{0.0};
    
    // Volume statistics
    Core::Volume total_volume{0};
    Core::Volume avg_volume{0};
    Core::Volume max_volume{0};
    
    // Return statistics
    double total_return{0.0};
    double annualized_return{0.0};
    double volatility{0.0};
    double sharpe_ratio{0.0};
    
    // Trading statistics
    std::size_t trading_days{0};
    std::size_t up_days{0};
    std::size_t down_days{0};
    double win_rate{0.0};
    
    // Additional metrics
    double max_drawdown{0.0};
    double beta{0.0};
    double correlation_to_market{0.0};
};

// Pattern recognition results
enum class PatternType : std::uint8_t {
    Doji = 1,
    Hammer = 2,
    ShootingStar = 3,
    Engulfing = 4,
    Harami = 5,
    Triangle = 6,
    HeadAndShoulders = 7,
    DoubleTop = 8,
    DoubleBottom = 9,
    Support = 10,
    Resistance = 11
};

struct PatternResult {
    PatternType type;
    Core::Symbol symbol;
    Core::Timestamp start_time;
    Core::Timestamp end_time;
    double confidence{0.0};
    std::string description;
    std::unordered_map<std::string, double> parameters;
};

using PatternResultVector = std::vector<PatternResult>;

// Data analyzer configuration
struct AnalyzerConfig {
    // Calculation settings
    std::size_t default_period{20};
    std::size_t max_lookback_days{252};
    
    // Performance settings
    bool enable_parallel_processing{true};
    std::size_t thread_count{4};
    
    // Caching settings
    bool enable_result_cache{true};
    std::size_t max_cache_size{1000};
    std::uint32_t cache_expire_minutes{30};
    
    // Pattern recognition settings
    double pattern_confidence_threshold{0.7};
    bool enable_advanced_patterns{true};
};

// Data analyzer interface
class IDataAnalyzer {
public:
    virtual ~IDataAnalyzer() = default;
    
    // Technical indicators
    virtual Core::Result<IndicatorResultVector> calculate_sma(
        const Core::Symbol& symbol,
        std::size_t period,
        const Core::Timestamp& start_time,
        const Core::Timestamp& end_time) = 0;
    
    virtual Core::Result<IndicatorResultVector> calculate_ema(
        const Core::Symbol& symbol,
        std::size_t period,
        const Core::Timestamp& start_time,
        const Core::Timestamp& end_time) = 0;
    
    virtual Core::Result<IndicatorResultVector> calculate_rsi(
        const Core::Symbol& symbol,
        std::size_t period,
        const Core::Timestamp& start_time,
        const Core::Timestamp& end_time) = 0;
    
    virtual Core::Result<IndicatorResultVector> calculate_macd(
        const Core::Symbol& symbol,
        std::size_t fast_period,
        std::size_t slow_period,
        std::size_t signal_period,
        const Core::Timestamp& start_time,
        const Core::Timestamp& end_time) = 0;
    
    virtual Core::Result<IndicatorResultVector> calculate_bollinger_bands(
        const Core::Symbol& symbol,
        std::size_t period,
        double std_dev_multiplier,
        const Core::Timestamp& start_time,
        const Core::Timestamp& end_time) = 0;
    
    // Market statistics
    virtual Core::Result<MarketStatistics> calculate_statistics(
        const Core::Symbol& symbol,
        const Core::Timestamp& start_time,
        const Core::Timestamp& end_time) = 0;
    
    virtual Core::Result<std::vector<MarketStatistics>> calculate_batch_statistics(
        const Core::SymbolVector& symbols,
        const Core::Timestamp& start_time,
        const Core::Timestamp& end_time) = 0;
    
    // Pattern recognition
    virtual Core::Result<PatternResultVector> detect_patterns(
        const Core::Symbol& symbol,
        const Core::Timestamp& start_time,
        const Core::Timestamp& end_time) = 0;
    
    virtual Core::Result<PatternResultVector> detect_candlestick_patterns(
        const Core::Symbol& symbol,
        const Core::Timestamp& start_time,
        const Core::Timestamp& end_time) = 0;
    
    virtual Core::Result<PatternResultVector> detect_chart_patterns(
        const Core::Symbol& symbol,
        const Core::Timestamp& start_time,
        const Core::Timestamp& end_time) = 0;
    
    // Correlation analysis
    virtual Core::Result<double> calculate_correlation(
        const Core::Symbol& symbol1,
        const Core::Symbol& symbol2,
        const Core::Timestamp& start_time,
        const Core::Timestamp& end_time) = 0;
    
    virtual Core::Result<std::unordered_map<Core::Symbol, double>> calculate_correlation_matrix(
        const Core::SymbolVector& symbols,
        const Core::Timestamp& start_time,
        const Core::Timestamp& end_time) = 0;
    
    // Risk analysis
    virtual Core::Result<double> calculate_var(
        const Core::Symbol& symbol,
        double confidence_level,
        std::size_t holding_period,
        const Core::Timestamp& start_time,
        const Core::Timestamp& end_time) = 0;
    
    virtual Core::Result<double> calculate_beta(
        const Core::Symbol& symbol,
        const Core::Symbol& benchmark,
        const Core::Timestamp& start_time,
        const Core::Timestamp& end_time) = 0;
    
    // Configuration
    virtual Core::Result<void> update_config(const AnalyzerConfig& config) = 0;
    virtual Core::Result<AnalyzerConfig> get_config() const = 0;
    
    // Cache management
    virtual Core::Result<void> clear_cache() = 0;
    virtual Core::Result<std::size_t> get_cache_size() const = 0;
};

// Data analyzer implementation
class DataAnalyzer : public IDataAnalyzer {
public:
    explicit DataAnalyzer(
        std::shared_ptr<Services::IDataHubManager> datahub,
        AnalyzerConfig config = {});
    
    ~DataAnalyzer() = default;
    
    // Disable copy, enable move
    DataAnalyzer(const DataAnalyzer&) = delete;
    DataAnalyzer& operator=(const DataAnalyzer&) = delete;
    DataAnalyzer(DataAnalyzer&&) noexcept = default;
    DataAnalyzer& operator=(DataAnalyzer&&) noexcept = default;
    
    // IDataAnalyzer implementation
    Core::Result<IndicatorResultVector> calculate_sma(
        const Core::Symbol& symbol,
        std::size_t period,
        const Core::Timestamp& start_time,
        const Core::Timestamp& end_time) override;
    
    Core::Result<IndicatorResultVector> calculate_ema(
        const Core::Symbol& symbol,
        std::size_t period,
        const Core::Timestamp& start_time,
        const Core::Timestamp& end_time) override;
    
    Core::Result<IndicatorResultVector> calculate_rsi(
        const Core::Symbol& symbol,
        std::size_t period,
        const Core::Timestamp& start_time,
        const Core::Timestamp& end_time) override;
    
    Core::Result<IndicatorResultVector> calculate_macd(
        const Core::Symbol& symbol,
        std::size_t fast_period,
        std::size_t slow_period,
        std::size_t signal_period,
        const Core::Timestamp& start_time,
        const Core::Timestamp& end_time) override;
    
    Core::Result<IndicatorResultVector> calculate_bollinger_bands(
        const Core::Symbol& symbol,
        std::size_t period,
        double std_dev_multiplier,
        const Core::Timestamp& start_time,
        const Core::Timestamp& end_time) override;
    
    Core::Result<MarketStatistics> calculate_statistics(
        const Core::Symbol& symbol,
        const Core::Timestamp& start_time,
        const Core::Timestamp& end_time) override;
    
    Core::Result<std::vector<MarketStatistics>> calculate_batch_statistics(
        const Core::SymbolVector& symbols,
        const Core::Timestamp& start_time,
        const Core::Timestamp& end_time) override;
    
    Core::Result<PatternResultVector> detect_patterns(
        const Core::Symbol& symbol,
        const Core::Timestamp& start_time,
        const Core::Timestamp& end_time) override;
    
    Core::Result<PatternResultVector> detect_candlestick_patterns(
        const Core::Symbol& symbol,
        const Core::Timestamp& start_time,
        const Core::Timestamp& end_time) override;
    
    Core::Result<PatternResultVector> detect_chart_patterns(
        const Core::Symbol& symbol,
        const Core::Timestamp& start_time,
        const Core::Timestamp& end_time) override;
    
    Core::Result<double> calculate_correlation(
        const Core::Symbol& symbol1,
        const Core::Symbol& symbol2,
        const Core::Timestamp& start_time,
        const Core::Timestamp& end_time) override;
    
    Core::Result<std::unordered_map<Core::Symbol, double>> calculate_correlation_matrix(
        const Core::SymbolVector& symbols,
        const Core::Timestamp& start_time,
        const Core::Timestamp& end_time) override;
    
    Core::Result<double> calculate_var(
        const Core::Symbol& symbol,
        double confidence_level,
        std::size_t holding_period,
        const Core::Timestamp& start_time,
        const Core::Timestamp& end_time) override;
    
    Core::Result<double> calculate_beta(
        const Core::Symbol& symbol,
        const Core::Symbol& benchmark,
        const Core::Timestamp& start_time,
        const Core::Timestamp& end_time) override;
    
    Core::Result<void> update_config(const AnalyzerConfig& config) override;
    Core::Result<AnalyzerConfig> get_config() const override;
    
    Core::Result<void> clear_cache() override;
    Core::Result<std::size_t> get_cache_size() const override;

private:
    std::shared_ptr<Services::IDataHubManager> datahub_;
    AnalyzerConfig config_;
    
    // Cache for calculated results
    mutable std::mutex cache_mutex_;
    std::unordered_map<std::string, IndicatorResultVector> indicator_cache_;
    std::unordered_map<std::string, MarketStatistics> statistics_cache_;
    std::unordered_map<std::string, PatternResultVector> pattern_cache_;
    
    // Helper methods
    Core::Result<Core::BarDataVector> get_bar_data(
        const Core::Symbol& symbol,
        const Core::Timestamp& start_time,
        const Core::Timestamp& end_time);
    
    std::string make_cache_key(const std::string& prefix,
                              const Core::Symbol& symbol,
                              const Core::Timestamp& start_time,
                              const Core::Timestamp& end_time,
                              const std::vector<std::string>& params = {});
    
    bool is_cache_valid(const std::string& key) const;
    void cleanup_expired_cache();
    
    // Mathematical calculations
    std::vector<double> extract_prices(const Core::BarDataVector& bars, const std::string& price_type = "close");
    std::vector<double> calculate_returns(const std::vector<double>& prices);
    double calculate_mean(const std::vector<double>& values);
    double calculate_std_deviation(const std::vector<double>& values, double mean);
    double calculate_correlation_coefficient(const std::vector<double>& x, const std::vector<double>& y);
    
    // Technical indicator calculations
    std::vector<double> calculate_sma_values(const std::vector<double>& prices, std::size_t period);
    std::vector<double> calculate_ema_values(const std::vector<double>& prices, std::size_t period);
    std::vector<double> calculate_rsi_values(const std::vector<double>& prices, std::size_t period);
    
    // Pattern detection algorithms
    bool is_doji(const Core::BarData& bar, double threshold = 0.1);
    bool is_hammer(const Core::BarData& bar);
    bool is_shooting_star(const Core::BarData& bar);
    bool is_engulfing_pattern(const Core::BarData& prev_bar, const Core::BarData& curr_bar);
    
    // Support and resistance detection
    std::vector<double> find_support_levels(const Core::BarDataVector& bars);
    std::vector<double> find_resistance_levels(const Core::BarDataVector& bars);
};

// Factory for creating analyzers
class AnalyzerFactory {
public:
    static std::unique_ptr<IDataAnalyzer> create(
        std::shared_ptr<Services::IDataHubManager> datahub,
        const AnalyzerConfig& config = {});
    
    static std::unique_ptr<IDataAnalyzer> create_with_custom_indicators(
        std::shared_ptr<Services::IDataHubManager> datahub,
        const std::vector<IndicatorType>& enabled_indicators,
        const AnalyzerConfig& config = {});
};

} // namespace DataHub::Analytics
