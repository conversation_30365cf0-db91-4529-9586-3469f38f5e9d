// ServiceFactory Implementation - Factory for creating DataHub services
#include "services/ServiceFactory.h"
#include "services/QuoteService.h"
#include "services/HistoryService_Enhanced.h"
#include "services/SecurityService_Enhanced.h"
#include "services/IndicatorService.h"
#include "services/EventBus.h"
#include "data/RepositoryFactory.h"

namespace DataHub::Services {

// ServiceFactory Implementation
std::unique_ptr<IQuoteService> ServiceFactory::create_quote_service(
    const QuoteServiceConfig& config,
    const std::string& database_path) {
    
    try {
        // Create repositories
        auto quote_repo = Data::RepositoryFactory::create_sqlite_repository(database_path);
        auto tick_repo = Data::RepositoryFactory::create_sqlite_repository(database_path);
        
        if (!quote_repo || !tick_repo) {
            return nullptr;
        }
        
        // Create service
        return std::make_unique<QuoteService>(quote_repo, tick_repo, config);
        
    } catch (const std::exception& e) {
        return nullptr;
    }
}

std::unique_ptr<IHistoryService> ServiceFactory::create_history_service(
    const HistoryServiceConfig& config,
    const std::string& database_path) {
    
    try {
        // Create repositories
        auto bar_repo = Data::RepositoryFactory::create_sqlite_repository(database_path);
        auto tick_repo = Data::RepositoryFactory::create_sqlite_repository(database_path);
        
        if (!bar_repo || !tick_repo) {
            return nullptr;
        }
        
        // Create service
        return std::make_unique<HistoryService>(bar_repo, tick_repo, config);
        
    } catch (const std::exception& e) {
        return nullptr;
    }
}

std::unique_ptr<ISecurityService> ServiceFactory::create_security_service(
    const SecurityServiceConfig& config,
    const std::string& database_path) {
    
    try {
        // Create repository
        auto security_repo = Data::RepositoryFactory::create_sqlite_repository(database_path);
        
        if (!security_repo) {
            return nullptr;
        }
        
        // Create service
        return std::make_unique<SecurityService>(security_repo, config);
        
    } catch (const std::exception& e) {
        return nullptr;
    }
}

std::unique_ptr<IIndicatorService> ServiceFactory::create_indicator_service(
    std::shared_ptr<IHistoryService> history_service,
    const IndicatorServiceConfig& config) {
    
    try {
        if (!history_service) {
            return nullptr;
        }
        
        return std::make_unique<IndicatorService>(history_service, config);
        
    } catch (const std::exception& e) {
        return nullptr;
    }
}

std::unique_ptr<IEventBus> ServiceFactory::create_event_bus(
    const EventBusConfig& config) {
    
    try {
        return std::make_unique<EventBus>(config);
        
    } catch (const std::exception& e) {
        return nullptr;
    }
}

// Complete service suite creation
ServiceSuite ServiceFactory::create_service_suite(
    const DataHubConfig& config) {
    
    ServiceSuite suite;
    
    try {
        // Create quote service
        suite.quote_service = create_quote_service(config.quote_config, config.database_path);
        if (!suite.quote_service) {
            return {}; // Return empty suite on failure
        }
        
        // Create history service
        suite.history_service = create_history_service(config.history_config, config.database_path);
        if (!suite.history_service) {
            return {};
        }
        
        // Create security service
        suite.security_service = create_security_service(config.security_config, config.database_path);
        if (!suite.security_service) {
            return {};
        }
        
        // Create indicator service
        IndicatorServiceConfig indicator_config;
        indicator_config.enable_cache = true;
        indicator_config.max_cache_size = 1000;
        
        suite.indicator_service = create_indicator_service(suite.history_service, indicator_config);
        if (!suite.indicator_service) {
            return {};
        }
        
        // Create event bus
        EventBusConfig event_config;
        event_config.max_queue_size = 10000;
        event_config.worker_thread_count = 2;
        event_config.enable_async_processing = true;
        
        suite.event_bus = create_event_bus(event_config);
        if (!suite.event_bus) {
            return {};
        }
        
        return suite;
        
    } catch (const std::exception& e) {
        return {}; // Return empty suite on any exception
    }
}

// Specialized factory methods
std::unique_ptr<IQuoteService> ServiceFactory::create_high_frequency_quote_service(
    const std::string& database_path) {
    
    QuoteServiceConfig config;
    config.update_interval_ms = 1;  // 1ms for high frequency
    config.max_cache_size = 50000;
    config.enable_realtime_push = true;
    config.enable_history_cache = true;
    config.cache_expire_seconds = 60;
    
    return create_quote_service(config, database_path);
}

std::unique_ptr<IHistoryService> ServiceFactory::create_compressed_history_service(
    const std::string& database_path) {
    
    HistoryServiceConfig config;
    config.enable_compression = true;
    config.compression_level = 9;  // Maximum compression
    config.max_memory_cache_mb = 1024;  // 1GB cache
    config.batch_size = 10000;
    
    return create_history_service(config, database_path);
}

std::unique_ptr<ISecurityService> ServiceFactory::create_enhanced_security_service(
    const std::string& database_path) {
    
    SecurityServiceConfig config;
    config.enable_auto_update = true;
    config.max_cache_size = 100000;
    config.enable_strict_validation = true;
    config.auto_fix_data = true;
    
    return create_security_service(config, database_path);
}

// Service validation
bool ServiceFactory::validate_service_suite(const ServiceSuite& suite) {
    return suite.quote_service != nullptr &&
           suite.history_service != nullptr &&
           suite.security_service != nullptr &&
           suite.indicator_service != nullptr &&
           suite.event_bus != nullptr;
}

Core::Result<void> ServiceFactory::start_service_suite(ServiceSuite& suite) {
    if (!validate_service_suite(suite)) {
        return Core::make_error<void>(Core::ErrorCode::InvalidOperation, "Invalid service suite");
    }
    
    try {
        // Start services in dependency order
        auto result = suite.event_bus->start();
        if (!result.is_success()) {
            return result;
        }
        
        result = suite.security_service->start();
        if (!result.is_success()) {
            return result;
        }
        
        result = suite.history_service->start();
        if (!result.is_success()) {
            return result;
        }
        
        result = suite.indicator_service->start();
        if (!result.is_success()) {
            return result;
        }
        
        result = suite.quote_service->start();
        if (!result.is_success()) {
            return result;
        }
        
        return Core::make_success();
        
    } catch (const std::exception& e) {
        return Core::make_error<void>(Core::ErrorCode::ServiceStartFailed, 
                                     "Failed to start service suite: " + std::string(e.what()));
    }
}

Core::Result<void> ServiceFactory::stop_service_suite(ServiceSuite& suite) {
    if (!validate_service_suite(suite)) {
        return Core::make_success(); // Already stopped or invalid
    }
    
    Core::Result<void> last_error = Core::make_success();
    
    try {
        // Stop services in reverse dependency order
        if (suite.quote_service) {
            auto result = suite.quote_service->stop();
            if (!result.is_success()) {
                last_error = result;
            }
        }
        
        if (suite.indicator_service) {
            auto result = suite.indicator_service->stop();
            if (!result.is_success()) {
                last_error = result;
            }
        }
        
        if (suite.history_service) {
            auto result = suite.history_service->stop();
            if (!result.is_success()) {
                last_error = result;
            }
        }
        
        if (suite.security_service) {
            auto result = suite.security_service->stop();
            if (!result.is_success()) {
                last_error = result;
            }
        }
        
        if (suite.event_bus) {
            auto result = suite.event_bus->stop();
            if (!result.is_success()) {
                last_error = result;
            }
        }
        
        return last_error;
        
    } catch (const std::exception& e) {
        return Core::make_error<void>(Core::ErrorCode::ServiceStopFailed,
                                     "Failed to stop service suite: " + std::string(e.what()));
    }
}

// Configuration helpers
QuoteServiceConfig ServiceFactory::get_default_quote_config() {
    QuoteServiceConfig config;
    config.update_interval_ms = 100;
    config.max_cache_size = 10000;
    config.enable_realtime_push = true;
    config.enable_history_cache = true;
    config.cache_expire_seconds = 300;
    return config;
}

HistoryServiceConfig ServiceFactory::get_default_history_config() {
    HistoryServiceConfig config;
    config.enable_compression = true;
    config.compression_level = 6;
    config.max_memory_cache_mb = 512;
    config.batch_size = 1000;
    return config;
}

SecurityServiceConfig ServiceFactory::get_default_security_config() {
    SecurityServiceConfig config;
    config.enable_auto_update = true;
    config.max_cache_size = 50000;
    config.enable_strict_validation = true;
    config.auto_fix_data = false;
    return config;
}

IndicatorServiceConfig ServiceFactory::get_default_indicator_config() {
    IndicatorServiceConfig config;
    config.enable_cache = true;
    config.max_cache_size = 1000;
    config.clear_cache_on_stop = false;
    return config;
}

EventBusConfig ServiceFactory::get_default_event_config() {
    EventBusConfig config;
    config.max_queue_size = 10000;
    config.worker_thread_count = 2;
    config.enable_async_processing = true;
    return config;
}

} // namespace DataHub::Services
