{"models": [{"id": "futures_pred_001", "name": "Futures Prediction Model", "description": "LightGBM model for futures price prediction", "type": "LightGBM", "model_path": "./models/futures_pred_001.txt", "config_path": "./models/futures_pred_001_config.json", "parameters": {"num_threads": 4, "objective": "regression", "metric": "rmse", "boosting_type": "gbdt", "num_leaves": 31, "learning_rate": 0.05, "feature_fraction": 0.9}, "feature_names": ["returns_1d", "returns_5d", "returns_20d", "volatility_20d", "volume_ratio", "rsi_14", "macd_signal", "bollinger_position", "momentum_score", "market_regime"], "categorical_features": ["market_regime"], "sequence_length": 20, "prediction_length": 1, "normalize_features": true, "normalization_method": "zscore", "use_gpu": false}, {"id": "stock_alpha_001", "name": "Stock Alpha Model", "description": "Multi-factor alpha model for stock selection", "type": "LightGBM", "model_path": "./models/stock_alpha_001.txt", "config_path": "./models/stock_alpha_001_config.json", "parameters": {"num_threads": 4, "objective": "regression", "metric": "rmse", "boosting_type": "gbdt", "num_leaves": 63, "learning_rate": 0.03, "feature_fraction": 0.8}, "feature_names": ["momentum_1m", "momentum_3m", "momentum_6m", "momentum_12m", "reversal_1d", "reversal_5d", "volatility_20d", "volatility_60d", "volume_20d", "turnover_20d", "pe_ratio", "pb_ratio", "roe", "roa", "debt_ratio", "market_cap", "sector_momentum", "industry_momentum"], "categorical_features": [], "sequence_length": 60, "prediction_length": 5, "normalize_features": true, "normalization_method": "zscore", "use_gpu": false}, {"id": "market_regime_001", "name": "Market Regime Model", "description": "Market regime classification model", "type": "LightGBM", "model_path": "./models/market_regime_001.txt", "config_path": "./models/market_regime_001_config.json", "parameters": {"num_threads": 2, "objective": "multiclass", "metric": "multi_logloss", "num_class": 4, "boosting_type": "gbdt", "num_leaves": 15, "learning_rate": 0.1, "feature_fraction": 0.9}, "feature_names": ["index_returns_1d", "index_returns_5d", "index_returns_20d", "index_volatility_20d", "vix_level", "term_spread", "credit_spread", "momentum_breadth", "volume_surge", "correlation_breakdown"], "categorical_features": [], "sequence_length": 20, "prediction_length": 1, "normalize_features": true, "normalization_method": "zscore", "use_gpu": false}, {"id": "stock_risk_001", "name": "Stock Risk Model", "description": "Risk factor model for stock portfolio", "type": "LightGBM", "model_path": "./models/stock_risk_001.txt", "config_path": "./models/stock_risk_001_config.json", "parameters": {"num_threads": 4, "objective": "regression", "metric": "rmse", "boosting_type": "gbdt", "num_leaves": 31, "learning_rate": 0.05, "feature_fraction": 0.9}, "feature_names": ["beta_market", "beta_size", "beta_value", "beta_momentum", "beta_quality", "beta_volatility", "sector_exposure", "industry_exposure", "specific_risk", "liquidity_risk"], "categorical_features": ["sector_exposure", "industry_exposure"], "sequence_length": 60, "prediction_length": 1, "normalize_features": true, "normalization_method": "zscore", "use_gpu": false}]}