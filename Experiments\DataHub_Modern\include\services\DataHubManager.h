#pragma once

#include "../core/Types.h"
#include "../data/IDataRepository_Simple.h"
#include "QuoteService.h"
#include "HistoryService_Enhanced.h"
#include "SecurityService_Enhanced.h"
#include <memory>
#include <unordered_map>
#include <string>
#include <functional>
#include <atomic>
#include <mutex>

namespace DataHub::Services {

// DataHub manager configuration
struct DataHubConfig {
    // Service configurations
    QuoteServiceConfig quote_config;
    HistoryServiceConfig history_config;
    SecurityServiceConfig security_config;
    
    // Global settings
    std::string data_directory{"./data"};
    std::string log_level{"info"};
    bool enable_metrics{true};
    bool enable_health_check{true};
    std::uint32_t health_check_interval_seconds{60};
    
    // Database settings
    std::string database_type{"sqlite"};
    std::string database_path{"./data/datahub.db"};
    std::uint32_t connection_pool_size{10};
    
    // Performance settings
    std::size_t max_concurrent_operations{100};
    std::uint32_t operation_timeout_seconds{30};
};

// Service health status
enum class ServiceHealth : std::uint8_t {
    Unknown = 0,
    Healthy = 1,
    Warning = 2,
    Critical = 3,
    Down = 4
};

// Service status information
struct ServiceStatus {
    std::string service_name;
    ServiceHealth health{ServiceHealth::Unknown};
    bool is_running{false};
    std::string status_message;
    Core::Timestamp last_check;
    std::unordered_map<std::string, std::string> metrics;
};

// System metrics
struct SystemMetrics {
    std::size_t total_memory_mb{0};
    std::size_t used_memory_mb{0};
    double cpu_usage_percent{0.0};
    std::size_t active_connections{0};
    std::size_t total_operations{0};
    std::size_t failed_operations{0};
    Core::Timestamp uptime_start;
    std::unordered_map<std::string, std::size_t> operation_counts;
};

// Event types for notifications
enum class EventType : std::uint8_t {
    ServiceStarted = 1,
    ServiceStopped = 2,
    ServiceError = 3,
    DataUpdated = 4,
    HealthChanged = 5,
    ConfigChanged = 6
};

// Event notification
struct Event {
    EventType type;
    std::string service_name;
    std::string message;
    Core::Timestamp timestamp;
    std::unordered_map<std::string, std::string> details;
};

// Event callback type
using EventCallback = std::function<void(const Event&)>;

// DataHub manager interface
class IDataHubManager {
public:
    virtual ~IDataHubManager() = default;
    
    // Lifecycle management
    virtual Core::Result<void> initialize(const DataHubConfig& config) = 0;
    virtual Core::Result<void> start() = 0;
    virtual Core::Result<void> stop() = 0;
    virtual Core::Result<void> shutdown() = 0;
    virtual bool is_running() const noexcept = 0;
    
    // Service access
    virtual std::shared_ptr<IQuoteService> get_quote_service() = 0;
    virtual std::shared_ptr<IHistoryService> get_history_service() = 0;
    virtual std::shared_ptr<ISecurityService> get_security_service() = 0;
    
    // Configuration management
    virtual Core::Result<void> update_config(const DataHubConfig& config) = 0;
    virtual Core::Result<DataHubConfig> get_config() const = 0;
    virtual Core::Result<void> reload_config(const std::string& config_file) = 0;
    virtual Core::Result<void> save_config(const std::string& config_file) const = 0;
    
    // Health monitoring
    virtual Core::Result<std::vector<ServiceStatus>> get_service_status() = 0;
    virtual Core::Result<ServiceStatus> get_service_status(const std::string& service_name) = 0;
    virtual Core::Result<SystemMetrics> get_system_metrics() = 0;
    virtual Core::Result<bool> health_check() = 0;
    
    // Event management
    virtual Core::Result<void> subscribe_events(EventCallback callback) = 0;
    virtual Core::Result<void> unsubscribe_events() = 0;
    
    // Data operations (unified interface)
    virtual Core::Result<Core::QuoteData> get_latest_quote(const Core::Symbol& symbol) = 0;
    virtual Core::Result<Core::BarDataVector> get_history_bars(
        const Core::Symbol& symbol,
        Core::BarSize bar_size,
        const Core::Timestamp& start_time,
        const Core::Timestamp& end_time) = 0;
    virtual Core::Result<Core::SecurityInfo> get_security_info(const Core::Symbol& symbol) = 0;
    
    // Batch operations
    virtual Core::Result<void> batch_save_quotes(const Core::QuoteDataVector& quotes) = 0;
    virtual Core::Result<void> batch_save_bars(const Core::BarDataVector& bars) = 0;
    virtual Core::Result<void> batch_save_securities(const Core::SecurityInfoVector& securities) = 0;
    
    // Maintenance operations
    virtual Core::Result<void> compact_database() = 0;
    virtual Core::Result<void> backup_data(const std::string& backup_path) = 0;
    virtual Core::Result<void> restore_data(const std::string& backup_path) = 0;
    virtual Core::Result<void> clear_cache() = 0;
};

// DataHub manager implementation
class DataHubManager : public IDataHubManager {
public:
    DataHubManager();
    ~DataHubManager();
    
    // Disable copy
    DataHubManager(const DataHubManager&) = delete;
    DataHubManager& operator=(const DataHubManager&) = delete;
    
    // Enable move
    DataHubManager(DataHubManager&&) noexcept;
    DataHubManager& operator=(DataHubManager&&) noexcept;
    
    // IDataHubManager implementation
    Core::Result<void> initialize(const DataHubConfig& config) override;
    Core::Result<void> start() override;
    Core::Result<void> stop() override;
    Core::Result<void> shutdown() override;
    bool is_running() const noexcept override;
    
    std::shared_ptr<IQuoteService> get_quote_service() override;
    std::shared_ptr<IHistoryService> get_history_service() override;
    std::shared_ptr<ISecurityService> get_security_service() override;
    
    Core::Result<void> update_config(const DataHubConfig& config) override;
    Core::Result<DataHubConfig> get_config() const override;
    Core::Result<void> reload_config(const std::string& config_file) override;
    Core::Result<void> save_config(const std::string& config_file) const override;
    
    Core::Result<std::vector<ServiceStatus>> get_service_status() override;
    Core::Result<ServiceStatus> get_service_status(const std::string& service_name) override;
    Core::Result<SystemMetrics> get_system_metrics() override;
    Core::Result<bool> health_check() override;
    
    Core::Result<void> subscribe_events(EventCallback callback) override;
    Core::Result<void> unsubscribe_events() override;
    
    Core::Result<Core::QuoteData> get_latest_quote(const Core::Symbol& symbol) override;
    Core::Result<Core::BarDataVector> get_history_bars(
        const Core::Symbol& symbol,
        Core::BarSize bar_size,
        const Core::Timestamp& start_time,
        const Core::Timestamp& end_time) override;
    Core::Result<Core::SecurityInfo> get_security_info(const Core::Symbol& symbol) override;
    
    Core::Result<void> batch_save_quotes(const Core::QuoteDataVector& quotes) override;
    Core::Result<void> batch_save_bars(const Core::BarDataVector& bars) override;
    Core::Result<void> batch_save_securities(const Core::SecurityInfoVector& securities) override;
    
    Core::Result<void> compact_database() override;
    Core::Result<void> backup_data(const std::string& backup_path) override;
    Core::Result<void> restore_data(const std::string& backup_path) override;
    Core::Result<void> clear_cache() override;
    
    // Static factory methods
    static std::unique_ptr<IDataHubManager> create();
    static std::unique_ptr<IDataHubManager> create_with_config(const DataHubConfig& config);

private:
    DataHubConfig config_;
    std::atomic<bool> initialized_;
    std::atomic<bool> running_;
    
    // Services
    std::shared_ptr<IQuoteService> quote_service_;
    std::shared_ptr<IHistoryService> history_service_;
    std::shared_ptr<ISecurityService> security_service_;
    
    // Data repositories
    std::shared_ptr<Data::IQuoteRepository> quote_repo_;
    std::shared_ptr<Data::IBarRepository> bar_repo_;
    std::shared_ptr<Data::ITickRepository> tick_repo_;
    std::shared_ptr<Data::ISecurityRepository> security_repo_;
    
    // Event management
    EventCallback event_callback_;
    mutable std::mutex event_mutex_;
    
    // Health monitoring
    std::unique_ptr<std::thread> health_monitor_thread_;
    std::atomic<bool> stop_health_monitor_;
    mutable std::mutex status_mutex_;
    std::unordered_map<std::string, ServiceStatus> service_status_;
    
    // Metrics
    SystemMetrics system_metrics_;
    Core::Timestamp startup_time_;
    std::atomic<std::size_t> operation_counter_;
    std::atomic<std::size_t> error_counter_;
    
    // Internal methods
    Core::Result<void> create_repositories();
    Core::Result<void> create_services();
    Core::Result<void> start_services();
    Core::Result<void> stop_services();
    
    void health_monitor_loop();
    void update_service_status();
    void update_system_metrics();
    void emit_event(const Event& event);
    
    ServiceHealth check_service_health(const std::string& service_name);
    std::string get_service_status_message(const std::string& service_name, ServiceHealth health);
    
    // Configuration helpers
    Core::Result<void> validate_config(const DataHubConfig& config) const;
    Core::Result<DataHubConfig> load_config_from_file(const std::string& config_file) const;
    Core::Result<void> save_config_to_file(const DataHubConfig& config, const std::string& config_file) const;
};

// Global DataHub instance management
class DataHubInstance {
public:
    static Core::Result<void> initialize(const DataHubConfig& config = {});
    static Core::Result<void> shutdown();
    static std::shared_ptr<IDataHubManager> get();
    static bool is_initialized();

private:
    static std::shared_ptr<IDataHubManager> instance_;
    static std::mutex instance_mutex_;
    static std::atomic<bool> initialized_;
};

} // namespace DataHub::Services
