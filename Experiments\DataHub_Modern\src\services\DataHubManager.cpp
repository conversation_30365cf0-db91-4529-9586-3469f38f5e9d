// DataHubManager Implementation - Core Business Logic
#include "services/DataHubManager.h"
#include "data/SqliteRepository.h"
#include <filesystem>
#include <fstream>

namespace DataHub::Services {

// DataHubManager Implementation
DataHubManager::DataHubManager()
    : initialized_(false)
    , running_(false)
    , startup_time_(std::chrono::system_clock::now())
    , operation_counter_(0)
    , error_counter_(0)
    , stop_health_monitor_(false) {
}

DataHubManager::~DataHubManager() {
    if (running_.load()) {
        stop();
    }
    if (initialized_.load()) {
        shutdown();
    }
}

DataHubManager::DataHubManager(DataHubManager&& other) noexcept
    : config_(std::move(other.config_))
    , initialized_(other.initialized_.load())
    , running_(other.running_.load())
    , quote_service_(std::move(other.quote_service_))
    , history_service_(std::move(other.history_service_))
    , security_service_(std::move(other.security_service_))
    , quote_repo_(std::move(other.quote_repo_))
    , bar_repo_(std::move(other.bar_repo_))
    , tick_repo_(std::move(other.tick_repo_))
    , security_repo_(std::move(other.security_repo_))
    , event_callback_(std::move(other.event_callback_))
    , health_monitor_thread_(std::move(other.health_monitor_thread_))
    , stop_health_monitor_(other.stop_health_monitor_.load())
    , service_status_(std::move(other.service_status_))
    , system_metrics_(std::move(other.system_metrics_))
    , startup_time_(other.startup_time_)
    , operation_counter_(other.operation_counter_.load())
    , error_counter_(other.error_counter_.load()) {
    
    other.initialized_ = false;
    other.running_ = false;
    other.stop_health_monitor_ = false;
}

DataHubManager& DataHubManager::operator=(DataHubManager&& other) noexcept {
    if (this != &other) {
        // Stop current instance
        if (running_.load()) {
            stop();
        }
        if (initialized_.load()) {
            shutdown();
        }
        
        config_ = std::move(other.config_);
        initialized_ = other.initialized_.load();
        running_ = other.running_.load();
        quote_service_ = std::move(other.quote_service_);
        history_service_ = std::move(other.history_service_);
        security_service_ = std::move(other.security_service_);
        quote_repo_ = std::move(other.quote_repo_);
        bar_repo_ = std::move(other.bar_repo_);
        tick_repo_ = std::move(other.tick_repo_);
        security_repo_ = std::move(other.security_repo_);
        event_callback_ = std::move(other.event_callback_);
        health_monitor_thread_ = std::move(other.health_monitor_thread_);
        stop_health_monitor_ = other.stop_health_monitor_.load();
        service_status_ = std::move(other.service_status_);
        system_metrics_ = std::move(other.system_metrics_);
        startup_time_ = other.startup_time_;
        operation_counter_ = other.operation_counter_.load();
        error_counter_ = other.error_counter_.load();
        
        other.initialized_ = false;
        other.running_ = false;
        other.stop_health_monitor_ = false;
    }
    return *this;
}

Core::Result<void> DataHubManager::initialize(const DataHubConfig& config) {
    if (initialized_.load()) {
        return Core::make_error<void>(Core::ErrorCode::InvalidOperation, "DataHub already initialized");
    }
    
    // Validate configuration
    auto validate_result = validate_config(config);
    if (!validate_result.is_success()) {
        return validate_result;
    }
    
    config_ = config;
    
    // Create data directory if it doesn't exist
    try {
        std::filesystem::create_directories(config_.data_directory);
    } catch (const std::exception& e) {
        return Core::make_error<void>(Core::ErrorCode::FileSystemError, 
                                     "Failed to create data directory: " + std::string(e.what()));
    }
    
    // Create repositories
    auto repo_result = create_repositories();
    if (!repo_result.is_success()) {
        return repo_result;
    }
    
    // Create services
    auto service_result = create_services();
    if (!service_result.is_success()) {
        return service_result;
    }
    
    // Initialize system metrics
    system_metrics_.uptime_start = startup_time_;
    
    initialized_ = true;
    
    // Emit initialization event
    Event init_event;
    init_event.type = EventType::ServiceStarted;
    init_event.service_name = "DataHubManager";
    init_event.message = "DataHub initialized successfully";
    init_event.timestamp = std::chrono::system_clock::now();
    emit_event(init_event);
    
    return Core::make_success();
}

Core::Result<void> DataHubManager::start() {
    if (!initialized_.load()) {
        return Core::make_error<void>(Core::ErrorCode::InvalidOperation, "DataHub not initialized");
    }
    
    if (running_.load()) {
        return Core::make_error<void>(Core::ErrorCode::InvalidOperation, "DataHub already running");
    }
    
    // Start services
    auto start_result = start_services();
    if (!start_result.is_success()) {
        return start_result;
    }
    
    // Start health monitor
    if (config_.enable_health_check) {
        stop_health_monitor_ = false;
        health_monitor_thread_ = std::make_unique<std::thread>(&DataHubManager::health_monitor_loop, this);
    }
    
    running_ = true;
    
    // Emit start event
    Event start_event;
    start_event.type = EventType::ServiceStarted;
    start_event.service_name = "DataHubManager";
    start_event.message = "DataHub started successfully";
    start_event.timestamp = std::chrono::system_clock::now();
    emit_event(start_event);
    
    return Core::make_success();
}

Core::Result<void> DataHubManager::stop() {
    if (!running_.load()) {
        return Core::make_success();
    }
    
    // Stop health monitor
    stop_health_monitor_ = true;
    if (health_monitor_thread_ && health_monitor_thread_->joinable()) {
        health_monitor_thread_->join();
    }
    health_monitor_thread_.reset();
    
    // Stop services
    auto stop_result = stop_services();
    if (!stop_result.is_success()) {
        // Log error but continue shutdown
        error_counter_++;
    }
    
    running_ = false;
    
    // Emit stop event
    Event stop_event;
    stop_event.type = EventType::ServiceStopped;
    stop_event.service_name = "DataHubManager";
    stop_event.message = "DataHub stopped";
    stop_event.timestamp = std::chrono::system_clock::now();
    emit_event(stop_event);
    
    return Core::make_success();
}

Core::Result<void> DataHubManager::shutdown() {
    if (running_.load()) {
        auto stop_result = stop();
        if (!stop_result.is_success()) {
            return stop_result;
        }
    }
    
    // Clear services and repositories
    quote_service_.reset();
    history_service_.reset();
    security_service_.reset();
    quote_repo_.reset();
    bar_repo_.reset();
    tick_repo_.reset();
    security_repo_.reset();
    
    initialized_ = false;
    
    return Core::make_success();
}

bool DataHubManager::is_running() const noexcept {
    return running_.load();
}

std::shared_ptr<IQuoteService> DataHubManager::get_quote_service() {
    return quote_service_;
}

std::shared_ptr<IHistoryService> DataHubManager::get_history_service() {
    return history_service_;
}

std::shared_ptr<ISecurityService> DataHubManager::get_security_service() {
    return security_service_;
}

Core::Result<void> DataHubManager::update_config(const DataHubConfig& config) {
    auto validate_result = validate_config(config);
    if (!validate_result.is_success()) {
        return validate_result;
    }
    
    config_ = config;
    
    // Update service configurations
    if (quote_service_) {
        quote_service_->update_config(config_.quote_config);
    }
    if (history_service_) {
        history_service_->update_config(config_.history_config);
    }
    if (security_service_) {
        security_service_->update_config(config_.security_config);
    }
    
    // Emit config change event
    Event config_event;
    config_event.type = EventType::ConfigChanged;
    config_event.service_name = "DataHubManager";
    config_event.message = "Configuration updated";
    config_event.timestamp = std::chrono::system_clock::now();
    emit_event(config_event);
    
    return Core::make_success();
}

Core::Result<DataHubConfig> DataHubManager::get_config() const {
    return Core::make_success(config_);
}

Core::Result<bool> DataHubManager::health_check() {
    operation_counter_++;
    
    if (!running_.load()) {
        return Core::make_success(false);
    }
    
    // Check all services
    bool all_healthy = true;
    
    if (quote_service_ && !quote_service_->is_running()) {
        all_healthy = false;
    }
    
    if (history_service_ && !history_service_->is_running()) {
        all_healthy = false;
    }
    
    if (security_service_ && !security_service_->is_running()) {
        all_healthy = false;
    }
    
    return Core::make_success(all_healthy);
}

Core::Result<SystemMetrics> DataHubManager::get_system_metrics() {
    update_system_metrics();
    return Core::make_success(system_metrics_);
}

Core::Result<void> DataHubManager::subscribe_events(EventCallback callback) {
    std::lock_guard<std::mutex> lock(event_mutex_);
    event_callback_ = std::move(callback);
    return Core::make_success();
}

Core::Result<void> DataHubManager::unsubscribe_events() {
    std::lock_guard<std::mutex> lock(event_mutex_);
    event_callback_ = nullptr;
    return Core::make_success();
}

// Unified data operations
Core::Result<Core::QuoteData> DataHubManager::get_latest_quote(const Core::Symbol& symbol) {
    operation_counter_++;
    
    if (!quote_service_) {
        error_counter_++;
        return Core::make_error<Core::QuoteData>(Core::ErrorCode::ServiceNotAvailable, "Quote service not available");
    }
    
    return quote_service_->get_latest_quote(symbol);
}

Core::Result<Core::BarDataVector> DataHubManager::get_history_bars(
    const Core::Symbol& symbol,
    Core::BarSize bar_size,
    const Core::Timestamp& start_time,
    const Core::Timestamp& end_time) {
    
    operation_counter_++;
    
    if (!history_service_) {
        error_counter_++;
        return Core::make_error<Core::BarDataVector>(Core::ErrorCode::ServiceNotAvailable, "History service not available");
    }
    
    return history_service_->get_bars(symbol, bar_size, Core::BarType::Time, start_time, end_time);
}

Core::Result<Core::SecurityInfo> DataHubManager::get_security_info(const Core::Symbol& symbol) {
    operation_counter_++;
    
    if (!security_service_) {
        error_counter_++;
        return Core::make_error<Core::SecurityInfo>(Core::ErrorCode::ServiceNotAvailable, "Security service not available");
    }
    
    return security_service_->get_security(symbol);
}

// Private methods
Core::Result<void> DataHubManager::create_repositories() {
    try {
        // Create SQLite repositories
        std::string db_path = config_.database_path;
        
        quote_repo_ = std::make_shared<Data::SqliteRepository>(db_path);
        bar_repo_ = std::make_shared<Data::SqliteRepository>(db_path);
        tick_repo_ = std::make_shared<Data::SqliteRepository>(db_path);
        security_repo_ = std::make_shared<Data::SqliteRepository>(db_path);
        
        return Core::make_success();
    } catch (const std::exception& e) {
        return Core::make_error<void>(Core::ErrorCode::InitializationFailed, 
                                     "Failed to create repositories: " + std::string(e.what()));
    }
}

Core::Result<void> DataHubManager::create_services() {
    try {
        // Create quote service
        quote_service_ = std::make_unique<QuoteService>(
            quote_repo_, tick_repo_, config_.quote_config);
        
        // Create history service
        history_service_ = std::make_unique<HistoryService>(
            bar_repo_, tick_repo_, config_.history_config);
        
        // Create security service
        security_service_ = std::make_unique<SecurityService>(
            security_repo_, config_.security_config);
        
        return Core::make_success();
    } catch (const std::exception& e) {
        return Core::make_error<void>(Core::ErrorCode::InitializationFailed,
                                     "Failed to create services: " + std::string(e.what()));
    }
}

Core::Result<void> DataHubManager::start_services() {
    // Start quote service
    if (quote_service_) {
        auto result = quote_service_->start();
        if (!result.is_success()) {
            return Core::make_error<void>(Core::ErrorCode::ServiceStartFailed,
                                         "Failed to start quote service: " + result.error().message);
        }
    }
    
    // Start history service
    if (history_service_) {
        auto result = history_service_->start();
        if (!result.is_success()) {
            return Core::make_error<void>(Core::ErrorCode::ServiceStartFailed,
                                         "Failed to start history service: " + result.error().message);
        }
    }
    
    // Start security service
    if (security_service_) {
        auto result = security_service_->start();
        if (!result.is_success()) {
            return Core::make_error<void>(Core::ErrorCode::ServiceStartFailed,
                                         "Failed to start security service: " + result.error().message);
        }
    }
    
    return Core::make_success();
}

Core::Result<void> DataHubManager::stop_services() {
    Core::Result<void> last_error = Core::make_success();
    
    // Stop services in reverse order
    if (security_service_) {
        auto result = security_service_->stop();
        if (!result.is_success()) {
            last_error = result;
        }
    }
    
    if (history_service_) {
        auto result = history_service_->stop();
        if (!result.is_success()) {
            last_error = result;
        }
    }
    
    if (quote_service_) {
        auto result = quote_service_->stop();
        if (!result.is_success()) {
            last_error = result;
        }
    }
    
    return last_error;
}

void DataHubManager::health_monitor_loop() {
    while (!stop_health_monitor_.load()) {
        try {
            update_service_status();
            update_system_metrics();
            
            // Sleep for health check interval
            std::this_thread::sleep_for(std::chrono::seconds(config_.health_check_interval_seconds));
        } catch (const std::exception& e) {
            error_counter_++;
            // Continue monitoring despite errors
        }
    }
}

void DataHubManager::update_service_status() {
    std::lock_guard<std::mutex> lock(status_mutex_);
    
    auto now = std::chrono::system_clock::now();
    
    // Update quote service status
    if (quote_service_) {
        ServiceStatus status;
        status.service_name = "QuoteService";
        status.is_running = quote_service_->is_running();
        status.health = status.is_running ? ServiceHealth::Healthy : ServiceHealth::Down;
        status.status_message = status.is_running ? "Running" : "Stopped";
        status.last_check = now;
        service_status_["QuoteService"] = status;
    }
    
    // Update history service status
    if (history_service_) {
        ServiceStatus status;
        status.service_name = "HistoryService";
        status.is_running = history_service_->is_running();
        status.health = status.is_running ? ServiceHealth::Healthy : ServiceHealth::Down;
        status.status_message = status.is_running ? "Running" : "Stopped";
        status.last_check = now;
        service_status_["HistoryService"] = status;
    }
    
    // Update security service status
    if (security_service_) {
        ServiceStatus status;
        status.service_name = "SecurityService";
        status.is_running = security_service_->is_running();
        status.health = status.is_running ? ServiceHealth::Healthy : ServiceHealth::Down;
        status.status_message = status.is_running ? "Running" : "Stopped";
        status.last_check = now;
        service_status_["SecurityService"] = status;
    }
}

void DataHubManager::update_system_metrics() {
    auto now = std::chrono::system_clock::now();
    auto uptime = std::chrono::duration_cast<std::chrono::seconds>(now - startup_time_);
    
    system_metrics_.total_operations = operation_counter_.load();
    system_metrics_.failed_operations = error_counter_.load();
    system_metrics_.active_connections = 1; // Simplified for now
    
    // Update operation counts
    system_metrics_.operation_counts["total"] = system_metrics_.total_operations;
    system_metrics_.operation_counts["failed"] = system_metrics_.failed_operations;
    system_metrics_.operation_counts["uptime_seconds"] = uptime.count();
}

void DataHubManager::emit_event(const Event& event) {
    std::lock_guard<std::mutex> lock(event_mutex_);
    if (event_callback_) {
        try {
            event_callback_(event);
        } catch (const std::exception& e) {
            error_counter_++;
            // Don't propagate callback errors
        }
    }
}

Core::Result<void> DataHubManager::validate_config(const DataHubConfig& config) const {
    if (config.data_directory.empty()) {
        return Core::make_error<void>(Core::ErrorCode::InvalidArgument, "Data directory cannot be empty");
    }
    
    if (config.database_path.empty()) {
        return Core::make_error<void>(Core::ErrorCode::InvalidArgument, "Database path cannot be empty");
    }
    
    if (config.health_check_interval_seconds == 0) {
        return Core::make_error<void>(Core::ErrorCode::InvalidArgument, "Health check interval must be greater than 0");
    }
    
    return Core::make_success();
}

// Static factory methods
std::unique_ptr<IDataHubManager> DataHubManager::create() {
    return std::make_unique<DataHubManager>();
}

std::unique_ptr<IDataHubManager> DataHubManager::create_with_config(const DataHubConfig& config) {
    auto manager = std::make_unique<DataHubManager>();
    auto init_result = manager->initialize(config);
    if (!init_result.is_success()) {
        return nullptr;
    }
    return manager;
}

// Global DataHub instance management
std::shared_ptr<IDataHubManager> DataHubInstance::instance_;
std::mutex DataHubInstance::instance_mutex_;
std::atomic<bool> DataHubInstance::initialized_{false};

Core::Result<void> DataHubInstance::initialize(const DataHubConfig& config) {
    std::lock_guard<std::mutex> lock(instance_mutex_);
    
    if (initialized_.load()) {
        return Core::make_error<void>(Core::ErrorCode::InvalidOperation, "DataHub instance already initialized");
    }
    
    instance_ = DataHubManager::create_with_config(config);
    if (!instance_) {
        return Core::make_error<void>(Core::ErrorCode::InitializationFailed, "Failed to create DataHub instance");
    }
    
    initialized_ = true;
    return Core::make_success();
}

Core::Result<void> DataHubInstance::shutdown() {
    std::lock_guard<std::mutex> lock(instance_mutex_);
    
    if (!initialized_.load()) {
        return Core::make_success();
    }
    
    if (instance_) {
        auto result = instance_->shutdown();
        instance_.reset();
        if (!result.is_success()) {
            return result;
        }
    }
    
    initialized_ = false;
    return Core::make_success();
}

std::shared_ptr<IDataHubManager> DataHubInstance::get() {
    std::lock_guard<std::mutex> lock(instance_mutex_);
    return instance_;
}

bool DataHubInstance::is_initialized() {
    return initialized_.load();
}

} // namespace DataHub::Services
