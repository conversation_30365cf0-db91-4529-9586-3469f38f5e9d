#pragma once

#include "../core/Types.h"
#include "../core/MarketData.h"
#include "../data/IDataRepository_Simple.h"
#include <memory>
#include <vector>
#include <unordered_map>
#include <functional>
#include <thread>
#include <atomic>
#include <mutex>

namespace DataHub::Services {

// History service configuration
struct HistoryServiceConfig {
    // Data compression settings
    bool enable_compression{true};
    std::uint32_t compression_level{6};
    
    // Cache settings
    std::size_t max_memory_cache_mb{512};
    std::uint32_t cache_expire_hours{24};
    
    // Performance settings
    std::size_t batch_size{1000};
    std::uint32_t query_timeout_seconds{30};
    
    // Storage settings
    std::string storage_path{"./data/history"};
    bool enable_auto_backup{true};
    std::uint32_t backup_interval_hours{6};
};

// Data aggregation types
enum class AggregationType : std::uint8_t {
    None = 0,
    OHLC = 1,      // Open, High, Low, Close
    VWAP = 2,      // Volume Weighted Average Price
    Statistics = 3  // Min, Max, Mean, StdDev
};

// Aggregation result
struct AggregationResult {
    Core::Symbol symbol;
    Core::Timestamp start_time;
    Core::Timestamp end_time;
    AggregationType type;
    
    // OHLC data
    Core::Price open{0.0};
    Core::Price high{0.0};
    Core::Price low{0.0};
    Core::Price close{0.0};
    Core::Volume volume{0};
    
    // VWAP data
    Core::Price vwap{0.0};
    
    // Statistical data
    Core::Price min_price{0.0};
    Core::Price max_price{0.0};
    Core::Price mean_price{0.0};
    double std_dev{0.0};
    std::size_t count{0};
};

// History service interface
class IHistoryService {
public:
    virtual ~IHistoryService() = default;
    
    // Service lifecycle
    virtual Core::Result<void> start() = 0;
    virtual Core::Result<void> stop() = 0;
    virtual bool is_running() const noexcept = 0;
    
    // Bar data operations
    virtual Core::Result<Core::BarDataVector> get_bars(
        const Core::Symbol& symbol,
        Core::BarSize bar_size,
        Core::BarType bar_type,
        const Core::Timestamp& start_time,
        const Core::Timestamp& end_time) = 0;
    
    virtual Core::Result<void> save_bars(const Core::BarDataVector& bars) = 0;
    virtual Core::Result<void> update_bar(const Core::BarData& bar) = 0;
    virtual Core::Result<void> delete_bars(
        const Core::Symbol& symbol,
        const Core::Timestamp& start_time,
        const Core::Timestamp& end_time) = 0;
    
    // Tick data operations
    virtual Core::Result<Core::TickDataVector> get_ticks(
        const Core::Symbol& symbol,
        const Core::Timestamp& start_time,
        const Core::Timestamp& end_time) = 0;
    
    virtual Core::Result<void> save_ticks(const Core::TickDataVector& ticks) = 0;
    virtual Core::Result<void> delete_ticks(
        const Core::Symbol& symbol,
        const Core::Timestamp& start_time,
        const Core::Timestamp& end_time) = 0;
    
    // Data aggregation
    virtual Core::Result<AggregationResult> aggregate_data(
        const Core::Symbol& symbol,
        AggregationType type,
        const Core::Timestamp& start_time,
        const Core::Timestamp& end_time) = 0;
    
    virtual Core::Result<Core::BarDataVector> aggregate_bars(
        const Core::Symbol& symbol,
        Core::BarSize from_size,
        Core::BarSize to_size,
        const Core::Timestamp& start_time,
        const Core::Timestamp& end_time) = 0;
    
    // Data management
    virtual Core::Result<void> compact_data(const Core::Symbol& symbol) = 0;
    virtual Core::Result<void> backup_data(const std::string& backup_path) = 0;
    virtual Core::Result<void> restore_data(const std::string& backup_path) = 0;
    
    // Statistics
    virtual Core::Result<std::size_t> get_data_count(const Core::Symbol& symbol) = 0;
    virtual Core::Result<std::size_t> get_storage_size(const Core::Symbol& symbol) = 0;
    virtual Core::Result<Core::Timestamp> get_earliest_time(const Core::Symbol& symbol) = 0;
    virtual Core::Result<Core::Timestamp> get_latest_time(const Core::Symbol& symbol) = 0;
};

// History service implementation
class HistoryService : public IHistoryService {
public:
    explicit HistoryService(
        std::shared_ptr<Data::IBarRepository> bar_repo,
        std::shared_ptr<Data::ITickRepository> tick_repo,
        HistoryServiceConfig config = {});
    
    ~HistoryService();
    
    // Disable copy
    HistoryService(const HistoryService&) = delete;
    HistoryService& operator=(const HistoryService&) = delete;
    
    // Enable move
    HistoryService(HistoryService&&) noexcept;
    HistoryService& operator=(HistoryService&&) noexcept;
    
    // IHistoryService implementation
    Core::Result<void> start() override;
    Core::Result<void> stop() override;
    bool is_running() const noexcept override;
    
    Core::Result<Core::BarDataVector> get_bars(
        const Core::Symbol& symbol,
        Core::BarSize bar_size,
        Core::BarType bar_type,
        const Core::Timestamp& start_time,
        const Core::Timestamp& end_time) override;
    
    Core::Result<void> save_bars(const Core::BarDataVector& bars) override;
    Core::Result<void> update_bar(const Core::BarData& bar) override;
    Core::Result<void> delete_bars(
        const Core::Symbol& symbol,
        const Core::Timestamp& start_time,
        const Core::Timestamp& end_time) override;
    
    Core::Result<Core::TickDataVector> get_ticks(
        const Core::Symbol& symbol,
        const Core::Timestamp& start_time,
        const Core::Timestamp& end_time) override;
    
    Core::Result<void> save_ticks(const Core::TickDataVector& ticks) override;
    Core::Result<void> delete_ticks(
        const Core::Symbol& symbol,
        const Core::Timestamp& start_time,
        const Core::Timestamp& end_time) override;
    
    Core::Result<AggregationResult> aggregate_data(
        const Core::Symbol& symbol,
        AggregationType type,
        const Core::Timestamp& start_time,
        const Core::Timestamp& end_time) override;
    
    Core::Result<Core::BarDataVector> aggregate_bars(
        const Core::Symbol& symbol,
        Core::BarSize from_size,
        Core::BarSize to_size,
        const Core::Timestamp& start_time,
        const Core::Timestamp& end_time) override;
    
    Core::Result<void> compact_data(const Core::Symbol& symbol) override;
    Core::Result<void> backup_data(const std::string& backup_path) override;
    Core::Result<void> restore_data(const std::string& backup_path) override;
    
    Core::Result<std::size_t> get_data_count(const Core::Symbol& symbol) override;
    Core::Result<std::size_t> get_storage_size(const Core::Symbol& symbol) override;
    Core::Result<Core::Timestamp> get_earliest_time(const Core::Symbol& symbol) override;
    Core::Result<Core::Timestamp> get_latest_time(const Core::Symbol& symbol) override;
    
    // Extended functionality
    Core::Result<void> update_config(const HistoryServiceConfig& config);
    Core::Result<HistoryServiceConfig> get_config() const;
    
    // Cache management
    Core::Result<void> clear_cache();
    Core::Result<void> preload_data(const Core::Symbol& symbol);
    
    // Data validation and repair
    Core::Result<std::vector<std::string>> validate_data(const Core::Symbol& symbol);
    Core::Result<void> repair_data(const Core::Symbol& symbol);

private:
    HistoryServiceConfig config_;
    std::atomic<bool> running_;
    
    std::shared_ptr<Data::IBarRepository> bar_repo_;
    std::shared_ptr<Data::ITickRepository> tick_repo_;
    
    // Cache management
    std::unordered_map<std::string, Core::BarDataVector> bar_cache_;
    std::unordered_map<std::string, Core::TickDataVector> tick_cache_;
    mutable std::mutex cache_mutex_;
    
    // Background tasks
    std::unique_ptr<std::thread> maintenance_thread_;
    std::atomic<bool> stop_maintenance_;
    
    // Internal methods
    void maintenance_loop();
    void cleanup_cache();
    void auto_backup();
    
    std::string make_cache_key(const Core::Symbol& symbol, 
                              const Core::Timestamp& start, 
                              const Core::Timestamp& end) const;
    
    bool is_cache_valid(const std::string& key) const;
    void update_cache(const std::string& key, const Core::BarDataVector& data);
    void update_cache(const std::string& key, const Core::TickDataVector& data);
    
    // Data processing
    AggregationResult calculate_ohlc(const Core::TickDataVector& ticks) const;
    AggregationResult calculate_vwap(const Core::TickDataVector& ticks) const;
    AggregationResult calculate_statistics(const Core::TickDataVector& ticks) const;
    
    Core::BarData aggregate_ticks_to_bar(const Core::TickDataVector& ticks, 
                                        Core::BarSize bar_size,
                                        Core::BarType bar_type) const;
    
    // Validation
    bool validate_bar(const Core::BarData& bar) const;
    bool validate_tick(const Core::TickData& tick) const;
    bool validate_time_range(const Core::Timestamp& start, const Core::Timestamp& end) const;
};

// History service factory
class HistoryServiceFactory {
public:
    static std::unique_ptr<IHistoryService> create(
        std::shared_ptr<Data::IBarRepository> bar_repo,
        std::shared_ptr<Data::ITickRepository> tick_repo,
        const HistoryServiceConfig& config = {});
    
    static std::unique_ptr<IHistoryService> create_with_storage(
        const std::string& storage_path,
        const HistoryServiceConfig& config = {});
};

} // namespace DataHub::Services
