# Test configuration for TradingSvr_Modern
cmake_minimum_required(VERSION 3.20)

# Find Google Test
find_package(GTest REQUIRED)
find_package(GMock REQUIRED)

# Include directories
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/../include
    ${GTEST_INCLUDE_DIRS}
    ${GMOCK_INCLUDE_DIRS}
)

# Test source files
file(GLOB_RECURSE TEST_SOURCES
    "*.cpp"
    "*.cc"
)

# Create test executable
add_executable(TradingSvr_Modern_Tests ${TEST_SOURCES})

# Link libraries
target_link_libraries(TradingSvr_Modern_Tests
    PRIVATE
        TradingSvr_Modern
        GTest::GTest
        GTest::Main
        GMock::GMock
        GMock::Main
        Threads::Threads
        Boost::system
        Boost::thread
        Boost::filesystem
)

# Compiler options
if(MSVC)
    target_compile_options(TradingSvr_Modern_Tests PRIVATE
        /W4 /WX- /bigobj
        /permissive-
        /Zc:__cplusplus
    )
else()
    target_compile_options(TradingSvr_Modern_Tests PRIVATE
        -Wall -Wextra -Wpedantic
        -Wno-unused-parameter
    )
endif()

# Add tests to CTest
include(GoogleTest)
gtest_discover_tests(TradingSvr_Modern_Tests)

# Test data directory
file(COPY ${CMAKE_CURRENT_SOURCE_DIR}/data
     DESTINATION ${CMAKE_CURRENT_BINARY_DIR})

# Coverage support (optional)
if(ENABLE_COVERAGE AND CMAKE_CXX_COMPILER_ID MATCHES "GNU|Clang")
    target_compile_options(TradingSvr_Modern_Tests PRIVATE --coverage)
    target_link_options(TradingSvr_Modern_Tests PRIVATE --coverage)
endif()
